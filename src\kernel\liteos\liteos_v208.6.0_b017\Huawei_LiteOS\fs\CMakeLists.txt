
set(MODULE_${LOSCFG_FS_COMPAT_NUTTX} compat)
list(APPEND MODULE_${LOSCFG_FS_VFS} vfs)
list(APPEND MODULE_${LOSCFG_FS_VFS_BLOCK_DEVICE} vfs/bch)
list(APPEND MODULE_${LOSCFG_DRIVER_DISK} vfs/disk)
list(APPEND MODULE_${LOSCFG_FS_FAT} fat)
list(APPEND MODULE_${LOSCFG_FS_FAT_VIRTUAL_PARTITION} fat/virpart)
list(APPEND MODULE_${LOSCFG_FS_FAT_CACHE} vfs/bcache)
list(APPEND MODULE_${LOSCFG_FS_FAT_UNI_TRIM} vfs/utrim)
list(APPEND MODULE_${LOSCFG_FS_FAT_FTL} vfs/ftl)
list(APPEND MODULE_${LOSCFG_FS_RAMFS} ramfs)
list(APPEND MODULE_${LOSCFG_FS_YAFFS} yaffs2)
list(APPEND MODULE_${LOSCFG_FS_NFS} nfs)
list(APPEND MODULE_${LOSCFG_FS_PROC} proc)
list(APPEND MODULE_${LOSCFG_FS_JFFS} jffs2)
list(APPEND MODULE_${LOSCFG_FS_ROMFS} romfs)
list(APPEND MODULE_${LOSCFG_FS_LITTLEFS} littlefs)

if(LOSCFG_COMPAT_LINUX)
    list(APPEND MODULE_${LOSCFG_FS_VFS} vfs/multi_partition)
endif()

FOREACH(CUR_MODULE_y ${MODULE_y})
    add_subdirectory(${CUR_MODULE_y})
ENDFOREACH(CUR_MODULE_y)
