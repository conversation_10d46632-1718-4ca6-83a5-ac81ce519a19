#===============================================================================
# @brief    Kconfig file.
# Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2024. All rights reserved.
#===============================================================================

choice
    prompt "Select AIR MOUSE type"
    default SAMPLE_SUPPORT_AIR_MOUSE
    config SAMPLE_SUPPORT_AIR_MOUSE
        bool "Enable AIR MOUSE sample."
        select SYSTEM_MOUSE_PIN_CONFIG
    config SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
        bool "Enable AIR MOUSE Dongle sample."
        choice
            prompt "Select Dongle type"
            depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
            default AIR_MOUSE_DONGLE_ABSOLUTE_COORDINATES
            config AIR_MOUSE_DONGLE_RELATIVE_COORDINATES
                bool "Switch to relative coordinates"
            config AIR_MOUSE_DONGLE_ABSOLUTE_COORDINATES
                bool "Switch to absolute coordinates"
            config AIR_MOUSE_DONGLE_FACTORY_PHASE_CALI
                bool "Switch to factory phase cali"
            config AIR_MOUSE_DONGLE_FACTORY_SCREEN_TEST
                bool "Switch to factory screen test"
        endchoice
endchoice

choice
    prompt "Select AIR MOUSE board"
    default AIR_MOUSE_SELF_BOARD
    config AIR_MOUSE_SELF_BOARD
        bool "Self Board."
    config AIR_MOUSE_HR_BOARD
        bool "HR Board."
    config AIR_MOUSE_EVB4_FOR_1T2R
        bool "Self EVB4 For 1T2R"
endchoice

choice
    prompt "Select slp usage mode"
    depends on SAMPLE_SUPPORT_AIR_MOUSE
    default SLP_USAGE_AIR_MOUSE
    config SLP_USAGE_RANGING_AOX
        bool "ranging aox."
    config SLP_USAGE_AIR_MOUSE
        bool "air mouse tv."
    config SLP_USAGE_AIR_MOUSE_CAR
        bool "air mouse car."
endchoice

config SAMPLE_SUPPORT_AIR_MOUSE_OTA
    bool
    default n
    prompt "Enable OTA service."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
    help
        This option means support SLE OTA service.

config SUPPORT_SLP_CIR_RPT
    bool
    default y
    prompt "Enable SLP CIR report."
    help
        This option means open cir print.

config SAMPLE_SUPPORT_AIR_MOUSE_CIR_PRINT
    bool
    default n
    prompt "Open CIR print."
    depends on SUPPORT_SLP_CIR_RPT
    help
        This option means open cir print.

config SAMPLE_SUPPORT_AIR_MOUSE_DEBUG
    bool
    default n
    prompt "Open debug print."
    help
        This option means print debug log.

config LOW_POWER_MODE
    hex
    default 0
    prompt "SLE SLP Low_Power 0-Disable 1-Enable."
    help
        This option means support SLE and SLP low power mode.

config SLE_DONGLE_SERVER_ADDR0
    hex
    default 0x0A
    prompt "Set the sle server addr[0]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
config SLE_DONGLE_SERVER_ADDR1
    hex
    default 0x01
    prompt "Set the sle server addr[1]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
config SLE_DONGLE_SERVER_ADDR2
    hex
    default 0x02
    prompt "Set the sle server addr[2]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
config SLE_DONGLE_SERVER_ADDR3
    hex
    default 0x03
    prompt "Set the sle server addr[3]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
config SLE_DONGLE_SERVER_ADDR4
    hex
    default 0x04
    prompt "Set the sle server addr[4]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE
config SLE_DONGLE_SERVER_ADDR5
    hex
    default 0x05
    prompt "Set the sle server addr[5]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_DONGLE

config SLE_MOUSE_SERVER_ADDR0
    hex
    default 0x0A
    prompt "Set the sle server addr[0]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
config SLE_MOUSE_SERVER_ADDR1
    hex
    default 0x01
    prompt "Set the sle server addr[1]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
config SLE_MOUSE_SERVER_ADDR2
    hex
    default 0x02
    prompt "Set the sle server addr[2]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
config SLE_MOUSE_SERVER_ADDR3
    hex
    default 0x03
    prompt "Set the sle server addr[3]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
config SLE_MOUSE_SERVER_ADDR4
    hex
    default 0x04
    prompt "Set the sle server addr[4]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE
config SLE_MOUSE_SERVER_ADDR5
    hex
    default 0x05
    prompt "Set the sle server addr[5]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE

config SLE_DONGLE_SERVER_ADDR0
    hex
    default 0x0A
    prompt "Set the sle server addr[0]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE
config SLE_DONGLE_SERVER_ADDR1
    hex
    default 0x01
    prompt "Set the sle server addr[1]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE
config SLE_DONGLE_SERVER_ADDR2
    hex
    default 0x02
    prompt "Set the sle server addr[2]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE
config SLE_DONGLE_SERVER_ADDR3
    hex
    default 0x03
    prompt "Set the sle server addr[3]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE
config SLE_DONGLE_SERVER_ADDR4
    hex
    default 0x04
    prompt "Set the sle server addr[4]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE
config SLE_DONGLE_SERVER_ADDR5
    hex
    default 0x05
    prompt "Set the sle server addr[5]."
    depends on SAMPLE_SUPPORT_AIR_MOUSE_GOLDEN_DONGLE

config AIR_MOUSE_UART_TX_PIN
    int
    range 26 26 if AIR_MOUSE_HR_BOARD
    range 30 30 if AIR_MOUSE_SELF_BOARD
    range 30 30 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the uart tx pin num."
config AIR_MOUSE_UART_RX_PIN
    int
    range 27 27 if AIR_MOUSE_HR_BOARD
    range 31 31 if AIR_MOUSE_SELF_BOARD
    range 31 31 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the uart rx pin num."
config AIR_MOUSE_UART_CTS_PIN
    int
    range 28 28 if AIR_MOUSE_HR_BOARD
    range 3 3 if AIR_MOUSE_SELF_BOARD
    range 3 3 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the uart cts pin num."
config AIR_MOUSE_UART_RTS_PIN
    int
    range 29 29 if AIR_MOUSE_HR_BOARD
    range 4 4 if AIR_MOUSE_SELF_BOARD
    range 4 4 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the uart rts pin num."
config AIR_MOUSE_POWER_ON_PIN
    int
    range 12 12 if AIR_MOUSE_HR_BOARD
    range 26 26 if AIR_MOUSE_SELF_BOARD
    range 26 26 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the slp power on pin num."
config AIR_MOUSE_POWER_CTRL_PIN
    int
    range 13 13 if AIR_MOUSE_HR_BOARD
    range 32 32 if AIR_MOUSE_SELF_BOARD
    range 2 2 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the slp power ctrl pin num."
config AIR_MOUSE_DEV_WAKEUP_HOST_PIN
    int
    range 15 15 if AIR_MOUSE_HR_BOARD
    range 29 29 if AIR_MOUSE_SELF_BOARD
    range 29 29 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the dev wakeup host pin num."
config AIR_MOUSE_SYNC_PIN
    int
    range 10 10 if AIR_MOUSE_HR_BOARD
    range 5 5 if AIR_MOUSE_SELF_BOARD
    range 5 5 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the sync pin num."
config AIR_MOUSE_IMU_INT2_PIN
    int
    range 14 14 if AIR_MOUSE_HR_BOARD
    range 32 32 if AIR_MOUSE_SELF_BOARD
    range 28 28 if AIR_MOUSE_EVB4_FOR_1T2R
    prompt "Set the imu int2 pin num."
