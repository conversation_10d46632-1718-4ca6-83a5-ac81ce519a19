[Patch Info]
Device_Code_Version = Version1

Patch_Cpu_Core = APPLICATION

Patch_File_Address  = 0x00000000
Patch_TBL_Address   = 0x00000300
Patch_TBL_Run_Address  = 0x00040000
Table_Max_Size         = 4
Table_Reg_Size         = 4
TABLE_REG_CONUT        = 128

[ROM Info]
ROM_Address        = 0x00015400
ROM_Size           = 0x0002ac00

[Output Info]
CMP_Bin_File        = cmp.bin
TBL_Bin_File        = tbl.bin
RW_Bin_File         = patch.bin

[Function]
####start platform patch
####end   platform patch

####start btc patch
#lm_ble_adv_init lm_ble_adv_init_patch
evt_task_ble_acl_refresh_next_peripheral_time evt_task_ble_acl_refresh_next_peripheral_time_patch
evt_task_ble_acl_process_rx_desc_isr evt_task_ble_acl_process_rx_desc_isr_patch
lm_ble_adv_set_rpa_sed lm_ble_adv_set_rpa_sed_patch
conn_update_calc_param_rsp conn_update_calc_param_rsp_patch
conn_update_calc_param_ind conn_update_calc_param_ind_patch
evt_task_ble_adv_set_intv evt_task_ble_adv_set_intv_patch
em_free_txpd em_free_txpd_patch
evt_task_ble_ext_adv_config_txpld evt_task_ble_ext_adv_config_txpld_patch
ble_ext_adv_comp_data_handle ble_ext_adv_comp_data_handle_patch
dts_malloc dts_malloc_patch
dts_hci_malloc dts_hci_malloc_patch
lm_gle_acb_set_iv lm_gle_acb_set_iv_patch
evt_task_ble_acl_tx_node_free evt_task_ble_acl_tx_node_free_patch
evt_task_ble_acl_event_int_tx_ack evt_task_ble_acl_event_int_tx_ack_patch
evt_task_ble_acl_cancel_cbk evt_task_ble_acl_cancel_cbk_patch
evt_task_ble_acl_save_sync_time_info evt_task_ble_acl_save_sync_time_info_patch
evt_task_ble_acl_check_time_out evt_task_ble_acl_check_time_out_patch
bgtp_sleep_wait_to_idle bgtp_sleep_wait_to_idle_patch
evt_task_ble_adv_get_stop_status evt_task_ble_adv_get_stop_status_patch
dpc_fsm_msg_process dpc_fsm_msg_process_patch
dpc_call_func dpc_call_func_patch
es_process_cancel_cbk es_process_cancel_cbk_patch
evt_prog_finish_eeq_isr evt_prog_finish_eeq_isr_patch
lm_ble_send_terminate_process lm_ble_send_terminate_process_patch
evt_task_ble_acl_set_interval evt_task_ble_acl_set_interval_patch
ble_isr_get_cur_isr_queue_head ble_isr_get_cur_isr_queue_head_patch
gle_isr_get_cur_isr_queue_head gle_isr_get_cur_isr_queue_head_patch
evt_task_ble_acl_update_param_with_instant evt_task_ble_acl_update_param_with_instant_patch
lm_ble_send_llcp_phy_rsp lm_ble_send_llcp_phy_rsp_patch
lm_ble_llcp_phy_req_act lm_ble_llcp_phy_req_act_patch
dm_ble_init_dft_val dm_ble_init_dft_val_patch
####end btc patch

####start bth patch
l2cap_le_link_encrypt_result l2cap_le_link_encrypt_result_patch
gatt_tl_update_item_tl_node gatt_tl_update_item_tl_node_patch
attc_pend_ind_response_singleitem attc_pend_ind_response_singleitem_patch
att_read_rsp att_read_rsp_patch
list_qos_check_cbk list_qos_check_cbk_patch
gaph_smp_cfm_cbk gaph_smp_cfm_cbk_patch
smp_key_distribute_go smp_key_distribute_go_patch
smp_key_distribute_recv smp_key_distribute_recv_patch
smp_tk_ask smp_tk_ask_patch
smp_require_end_key_recovery smp_require_end_key_recovery_patch
att_handle_value_confirmation att_handle_value_confirmation_patch
gattc_tl_timeout gattc_tl_timeout_patch

bth_init bth_init_patch
bt_init_stack bt_init_stack_patch
smp_rand smp_rand_patch
smp_ecdh_pairing_public_key_recv smp_ecdh_pairing_public_key_recv_patch
smp_ecdh_public_key_generate smp_ecdh_public_key_generate_patch
smp_require_end smp_require_end_patch

_list_s_node_new _list_s_node_new_patch
_list_node_new _list_node_new_patch
att_uuid_len att_uuid_len_patch
####end bth patch
