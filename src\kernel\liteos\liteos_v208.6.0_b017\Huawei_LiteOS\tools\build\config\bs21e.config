# Generated by LiteOS Kconfig Tool
#
# Compiler
#
LOSCFG_COMPILER_GNU_BINUTILS=y
LOSCFG_COMPILER_GCC=y
LOSCFG_COMPILER_TOOLCHAIN_UNKNOWN=y
# LOSCFG_COMPILER_RISCV_GCC_MUSL is not set
LOSCFG_COMPILER_RISCV_GCC_UNKNOWN=y
# LOSCFG_COMPILER_RISCV_CLANG_MUSL is not set

#
# Compiler Options
#
LOSCFG_RISCV_COMPILER_OPTIONS_USER_DEFINED="--short-enums;-ffreestanding;-fdata-sections;-ffunction-sections;-pipe;-fno-tree-scev-cprop;-fno-common;-mpush-pop;-msmall-data-limit=0;-fno-ipa-ra;-Wtrampolines;-Wlogical-op;-Wjump-misses-init;-Wa,-enable-c-lbu-sb;-Wa,-enable-c-lhu-sh;-fimm-compare;-femit-muliadd;-fmerge-immshf;-femit-uxtb-uxth;-femit-lli;-femit-clz;-fldm-stm-optimize;-madjust-regorder;-madjust-const-cost;-freorder-commu-args;-fimm-compare-expand;-frmv-str-zero;-mfp-const-opt;-mswitch-jump-table;-frtl-sequence-abstract;-frtl-hoist-sink;-fsafe-alias-multipointer;-finline-optimize-size;-fmuliadd-expand;-mlli-expand;-Wa,-mcjal-expand;-foptimize-reg-alloc;-fsplit-multi-zero-assignments;-floop-optimize-size;-mpattern-abstract;-foptimize-pro-and-epilogue"
LOSCFG_RISCV_COMPILER_OPTIONS_LDM_STM=y
LOSCFG_RISCV_COMPILER_OPTIONS_EMIT_LLI=y
# LOSCFG_RISCV_COMPILER_OPTIONS_MSMALL_DATA_LIMIT0 is not set
# LOSCFG_RISCV_COMPILER_OPTIONS_LBU_SB is not set
# LOSCFG_RISCV_COMPILER_OPTIONS_NO_INLINE is not set
# LOSCFG_RISCV_COMPILER_OPTIONS_SAVE_RESTORE is not set
LOSCFG_RISCV_COMPILER_OPTIONS_PUSH_POP=y
# end of Compiler Options

# LOSCFG_COMPILER_OPTIMIZE_NONE is not set
# LOSCFG_COMPILER_OPTIMIZE_SPEED is not set
LOSCFG_COMPILER_OPTIMIZE_SIZE=y
# LOSCFG_COMPILER_THIRDPARTY_SUPPORT is not set
# end of Compiler

#
# Targets
#
# LOSCFG_FAMILY_BVT is not set
# LOSCFG_FAMILY_DPT is not set
LOSCFG_FAMILY_AIOT=y
# LOSCFG_FAMILY_STB is not set
# LOSCFG_FAMILY_QEMU is not set
# LOSCFG_FAMILY_STM32 is not set
# LOSCFG_FAMILY_SECRET is not set
# LOSCFG_FAMILY_HQ is not set
# LOSCFG_FAMILY_ENERGY is not set
# LOSCFG_FAMILY_SHC is not set
# LOSCFG_FAMILY_ASCEND is not set
# LOSCFG_FAMILY_DATACOM is not set
LOSCFG_FAMILY="aiot"
LOSCFG_PLATFORM="bs21"
# LOSCFG_PLATFORM_BS20 is not set
LOSCFG_PLATFORM_BS21=y
# LOSCFG_PLATFORM_BS21A is not set
# LOSCFG_PLATFORM_BS22 is not set
# LOSCFG_PLATFORM_BS25 is not set
# LOSCFG_PLATFORM_BS26 is not set
# LOSCFG_PLATFORM_WS63 is not set
# LOSCFG_PLATFORM_SUQIN_APP is not set
# LOSCFG_PLATFORM_SOCMN1 is not set
# LOSCFG_PLATFORM_SW39_BT is not set
LOSCFG_USING_BOARD_LD=y
LOSCFG_USING_BOARD_RESET_VECTOR=y
LOSCFG_ARCH_FPU_ENABLE=y
# LOSCFG_APC_ENABLE is not set
# LOSCFG_FPB_ENABLE is not set
LOSCFG_ARCH_PMU=y
LOSCFG_ARCH_RISCV32=y
LOSCFG_ARCH_RISCV_RV32IMC=y
LOSCFG_ARCH_RISCV_ISA_F=y
LOSCFG_ARCH_LINXCORE_131=y
# end of Targets

#
# Kernel
#
LOSCFG_KERNEL_MIN=y

#
# Basic Config
#
LOSCFG_SCHED=y
LOSCFG_SCHED_SQ=y

#
# Task
#
# LOSCFG_TASK_JOINABLE is not set
# LOSCFG_BASE_CORE_TIMESLICE is not set
# LOSCFG_OBSOLETE_API is not set
LOSCFG_BASE_CORE_TSK_MONITOR=y
# LOSCFG_TASK_STACK_STATIC_ALLOCATION is not set
LOSCFG_TASK_STACK_DYNAMIC_ALLOCATION=y
LOSCFG_BASE_CORE_TSK_LIMIT=15
LOSCFG_BASE_CORE_TSK_MIN_STACK_SIZE=512
LOSCFG_BASE_CORE_TSK_DEFAULT_STACK_SIZE=2048
LOSCFG_BASE_CORE_TSK_SWTMR_STACK_SIZE=2048
LOSCFG_BASE_CORE_TSK_IDLE_STACK_SIZE=1536
LOSCFG_BASE_CORE_TSK_DEFAULT_PRIO=10
LOSCFG_BASE_CORE_TICK_PER_SECOND=1000
# end of Task

# LOSCFG_BASE_CORE_USE_MULTI_LIST is not set
LOSCFG_BASE_CORE_USE_SINGLE_LIST=y
LOSCFG_STARTUP_STACK_SIZE=0x4000
# end of Basic Config

#
# Memory Management
#
LOSCFG_KERNEL_MEM_ALLOC=y
# LOSCFG_KERNEL_MEM_BESTFIT is not set
LOSCFG_KERNEL_MEM_BESTFIT_LITTLE=y
# LOSCFG_KERNEL_MEM_SLAB_EXTENTION is not set
# LOSCFG_MEM_TASK_STAT is not set
# LOSCFG_MEM_DFX_SHOW_CALLER_RA is not set
# LOSCFG_KERNEL_MEMBOX is not set
# end of Memory Management

#
# Interrupt Management
#
LOSCFG_ARCH_INTERRUPT_TAKEOVER=y
LOSCFG_ARCH_INTERRUPT_PREEMPTION=y
LOSCFG_HWI_PRE_POST_PROCESS=y
LOSCFG_HWI_WITH_ARG=y
# LOSCFG_SHARED_IRQ is not set
# LOSCFG_HWI_BOTTOM_HALF is not set
LOSCFG_IRQ_STACK_SIZE=0x2000
LOSCFG_NMI_STACK_SIZE=0x800
LOSCFG_PLATFORM_HWI_LIMIT=90
LOSCFG_HWI_PRIO_LIMIT=7
# end of Interrupt Management

#
# Exception Management
#
LOSCFG_EXC_SIMPLE_INFO=y
LOSCFG_EXC_STACK_SIZE=0x800
# LOSCFG_SHELL_EXCINFO_DUMP is not set
# end of Exception Management

# LOSCFG_LASLR is not set
LOSCFG_BASE_CORE_SWTMR=y
LOSCFG_BASE_CORE_SWTMR_LIMIT=40
# LOSCFG_BASE_CORE_SWTMR_IN_ISR is not set
# LOSCFG_SWTMR_SYNC_DELETE is not set
LOSCFG_BASE_IPC_QUEUE=y
# LOSCFG_QUEUE_STATIC_ALLOCATION is not set
LOSCFG_QUEUE_DYNAMIC_ALLOCATION=y
LOSCFG_BASE_IPC_QUEUE_LIMIT=10
LOSCFG_BASE_IPC_EVENT=y
LOSCFG_BASE_IPC_MUX=y
LOSCFG_MUTEX_WAITMODE_PRIO=y
# LOSCFG_MUTEX_WAITMODE_FIFO is not set
LOSCFG_BASE_IPC_MUX_LIMIT=20
LOSCFG_BASE_IPC_SEM=y
LOSCFG_BASE_IPC_SEM_LIMIT=10
# LOSCFG_BASE_IPC_RWSEM is not set
# LOSCFG_KERNEL_NX is not set
LOSCFG_KERNEL_PRINTF=y
LOSCFG_KERNEL_PRINTF_SIZE_EXTEND=y
# LOSCFG_KERNEL_RINGBUF is not set
# LOSCFG_KERNEL_EXTKERNEL is not set
# LOSCFG_BASE_CORE_SYS_RES_CHECK is not set
# end of Kernel

#
# Lib
#
# LOSCFG_LIB_LIBSEC is not set
LOSCFG_LIB_LIBC=y
# LOSCFG_COMPAT_POSIX is not set
LOSCFG_LIB_LIBM=y
LOSCFG_LIB_FORMAT=y
# LOSCFG_SUPPORT_LONG_DOUBLE is not set
# LOSCFG_LIB_ZLIB is not set
# end of Lib

#
# Compat
#
LOSCFG_COMPAT_CMSIS=y
# LOSCFG_COMPAT_CMSIS_VER_1 is not set
LOSCFG_COMPAT_CMSIS_VER_2=y
# LOSCFG_COMPAT_LINUX is not set
# end of Compat

#
# FileSystem
#
# LOSCFG_FS_COMPAT_NUTTX is not set
# LOSCFG_FS_VFS is not set
# end of FileSystem

#
# Net
#
# LOSCFG_NET_LWIP_SACK is not set
# end of Net

#
# Security
#

#
# Debug
#
# LOSCFG_COMPILE_DEBUG is not set
# LOSCFG_PLATFORM_ADAPT is not set
# LOSCFG_BACKTRACE is not set
# LOSCFG_ENABLE_MAGICKEY is not set
# LOSCFG_DEBUG_VERSION is not set
# LOSCFG_SERIAL_OUTPUT_ENABLE is not set
# LOSCFG_APPINIT_TESTSUIT is not set
# LOSCFG_KERNEL_LMS is not set
# LOSCFG_TIMER_DEBUG is not set
# LOSCFG_MUTEX_DEBUG is not set
# LOSCFG_SEMAPHORE_DEBUG is not set
# end of Debug

#
# Driver
#
LOSCFG_DRIVERS_BASE=y
LOSCFG_RISCV_HIMIDEERV200_PLIC=y
LOSCFG_TIMER_VENDOR=y
LOSCFG_DRIVERS_UART_VENDOR=y
# LOSCFG_DRIVERS_UART is not set
LOSCFG_DRIVERS_SIMPLE_UART=y
# LOSCFG_DRIVERS_USB is not set
LOSCFG_DRIVERS_USB_DEVICE_NO_DRIVER_BASE=y
# end of Driver

#
# Stack Smashing Protector (SSP) Compiler Feature
#
# LOSCFG_CC_NO_STACKPROTECTOR is not set
# LOSCFG_CC_STACKPROTECTOR is not set
LOSCFG_CC_STACKPROTECTOR_STRONG=y
# LOSCFG_CC_STACKPROTECTOR_ALL is not set
# end of Stack Smashing Protector (SSP) Compiler Feature
