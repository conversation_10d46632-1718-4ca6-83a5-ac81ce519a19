@import url(../iconfont/iconfont.css);
*,
:after,
:before {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
}

.ellipse {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body {
  height: 100%;
  background-color: unset;
}

body .wy-grid-for-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
}

body .wy-grid-for-nav .wy-nav-side {
  position: relative;
  width: 360px;
  background: #F3F3F3;
  color: #777;
  padding: 0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll {
  padding: 0;
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search {
  width: 100%;
  background: unset;
  padding: 0;
  color: inherit;
  margin-bottom: 0;
  text-align: left;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search > a {
  display: block;
  padding: 0;
  margin: 0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search > a:before {
  content: none;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search > a img {
  margin: 0 auto;
  padding: 0;
  margin-top: 32px;
  margin-bottom: 24px;
  width: 194px;
  height: 48px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search > a .projectTitle {
  color: #000;
  text-align: center;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search > a:hover {
  background: unset;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .projectTitle {
  font-weight: bold;
  font-size: 20px;
  line-height: 30px;
  margin-bottom: 24px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors {
  margin: 36px 24px 24px 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .repo-readOnly {
  font-weight: bold;
  font-size: 20px;
  color: #191919;
  max-width: 124px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-readOnly {
  font-size: 12px;
  line-height: 18px;
  color: #191919;
  max-width: 84px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors input {
  border: 0;
  color: #191919;
  background-color: inherit;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  border-radius: 0;
  z-index: -1;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors input#repo-select {
  font-weight: bold;
  font-size: 20px;
  width: 0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors input#version-select {
  width: 0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .select {
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 4px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .select .icon-xiala {
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .select.active .icon-xiala {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .repo-select,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: 100%;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .repo-select .text,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select .text {
  font-size: 12px;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select.only-version {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select.only-version .text {
  font-size: 14px;
  line-height: 22px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors .version-select.only-version .version-readOnly {
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
  max-width: 242px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #repo-options,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #version-options {
  max-width: 312px;
  position: absolute;
  top: 24px;
  background: #FFF;
  z-index: 1;
  padding: 4px;
  color: #191919;
  border-radius: 8px;
  display: none;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #repo-options .opTitle,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #version-options .opTitle {
  color: #c3c3c3;
  padding: 4px 8px;
  font-size: 12px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #repo-options .option,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #version-options .option {
  font-size: 14px;
  line-height: 22px;
  border-radius: 4px;
  padding: 7px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #repo-options .option:hover,
body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #version-options .option:hover {
  background-color: #e1e8fd;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .selectors #version-options {
  min-width: 212px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .wy-form {
  padding: 0;
  margin: 0 24px;
  padding-top: 24px;
  border-top: 1px solid #dfdfdf;
  position: relative;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .wy-form::after {
  font-family: "iconfont";
  content: '\e690';
  font-size: 14px;
  position: absolute;
  top: 33px;
  left: 12px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .wy-form input[type=text] {
  border-radius: 8px;
  border: 1px solid #c9c9c9;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  position: relative;
  padding-left: 34px;
  height: 32px;
  font-size: 14px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-side-nav-search .wy-form input[type=text]:hover {
  border-color: #5e7ce0;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical {
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  padding: 12px 24px 30px 24px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul {
  width: 100%;
  overflow: auto;
  scroll-behavior: smooth;
  /* Track */
  /* Handle */
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul::-webkit-scrollbar {
  width: 6px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul::-webkit-scrollbar-track {
  border-radius: 4px;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #dfdfdf;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li {
  background: inherit;
  color: inherit;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li a {
  border: 0;
  border-radius: 8px;
  color: inherit;
  background: inherit;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li a button {
  color: inherit;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li button.toctree-expand::before {
  font-family: 'iconfont';
  content: '\e696';
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li.current > a {
  font-weight: inherit;
  color: inherit;
  background-color: inherit;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li.current > a button.toctree-expand::before {
  font-family: 'iconfont';
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  content: '\e696';
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li a:hover {
  background: rgba(25, 25, 25, 0.05);
  color: #191919;
  font-weight: bold;
}

body .wy-grid-for-nav .wy-nav-side .wy-side-scroll .wy-menu-vertical ul li a.current {
  background: #e1e8fd;
  color: #4965c3;
  font-weight: bold;
}

body .wy-grid-for-nav .wy-nav-content-wrap {
  margin: 0;
  position: relative;
  width: calc(100% - 360px);
  height: 100%;
  background-color: #fff;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content {
  max-width: unset;
  width: 100%;
  overflow: auto;
  padding: 60px;
  scroll-behavior: smooth;
  background: #fff;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content {
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content div[role=navigation] {
  padding-bottom: 32px;
  border-bottom: 1px solid #c9c9c9;
  margin-bottom: 8px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content div[role=navigation] hr {
  display: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs {
  color: #777;
  font-size: 12px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs .icon-home {
  font-family: 'iconfont';
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs .icon-home:before {
  font-family: inherit;
  content: '\e68c';
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs li.active {
  color: #191919;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs a {
  color: #777;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs .wy-breadcrumbs-aside .fa-github {
  color: #4965c3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  line-height: 24px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs .wy-breadcrumbs-aside .fa-github .HiSpark {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .wy-breadcrumbs .wy-breadcrumbs-aside .fa-github::before {
  content: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document blockquote,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document figure,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document form,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h1,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h2,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h3,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h4,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h5,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h6,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document p {
  margin: 16px 0;
  font-weight: normal;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h1,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h2,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h3,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h4,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h5,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h6 {
  margin-top: 24px;
  font-weight: bold;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h1 {
  font-size: 32px;
  line-height: 48px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h2 {
  font-size: 28px;
  line-height: 48px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h3 {
  font-size: 24px;
  line-height: 36px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h4 {
  font-size: 20px;
  line-height: 30px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h5 {
  font-size: 16px;
  line-height: 24px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document h6 {
  font-size: 12px;
  line-height: 20px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document a {
  color: #5e7ce0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document p {
  font-size: 14px;
  line-height: 22px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document table th,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document table td {
  padding: 7px 16px;
  border: 1px solid #dfdfdf;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document table p {
  padding: 0;
  margin: 0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document table th p {
  font-weight: bold;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document .headerlink {
  color: transparent;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content .document .headerlink::before {
  font-family: 'iconfont';
  content: '\e697';
  color: #7693f5;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer {
  margin-top: 32px;
  color: #777;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer hr {
  display: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .feedback {
  background-color: transparent;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert {
  padding: 28px 32px;
  color: #191919;
  -webkit-box-shadow: 0 16px 48px rgba(0, 0, 0, 0.16);
          box-shadow: 0 16px 48px rgba(0, 0, 0, 0.16);
  width: 920px;
  min-width: none;
  max-width: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  line-height: 24px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .header .title {
  font-weight: bold;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .header .icon-close {
  position: static;
  background: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content {
  margin-top: 24px;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  overflow: auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 16px;
  width: 100%;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 8px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .form-item-label {
  position: relative;
  font-size: 14px;
  line-height: 22px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .form-item-label.required::before {
  content: '* ';
  color: #e7625d;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .textarea-box {
  margin: 0;
  min-height: unset;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .textarea-box textarea {
  padding: 5px 12px;
  min-height: 132px;
  border: 1px solid #c9c9c9;
  border-radius: 4px;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  font-size: 14px;
  line-height: 22px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .textarea-box textarea:hover, body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .textarea-box textarea:focus {
  border-color: #5e7ce0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .switch {
  background: #F3F3F3;
  border-radius: 6px;
  padding: 2px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .switch .switch-item {
  border: 0;
  border-radius: 4px;
  padding: 3px 16px;
  background-color: inherit;
  color: #777;
  line-height: 22px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .content form .form-item .switch .switch-item.selected {
  background: #fff;
  color: #5e7ce0;
  font-weight: bold;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
          box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer {
  margin-top: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  gap: 8px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 1px solid #c9c9c9;
  border-radius: 4px;
  background: #fff;
  color: #191919;
  padding: 5px 30px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button:hover {
  border-color: #191919;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button:active {
  border-color: #5e7ce0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button.btn-submit {
  margin: 0;
  font-size: unset;
  line-height: unset;
  height: unset;
  width: unset;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button.primary {
  background-color: #5e7ce0;
  color: #fff;
  border: 0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button.primary:hover {
  background-color: #7693f5;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .alert .footer button.primary:active {
  background-color: #465eb8;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .feedback-content {
  width: 32px;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #5e7ce0;
  border-radius: 8px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .feedback-content .iconfont {
  color: #fff;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .feedback-content:hover {
  background-color: #7693f5;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .feedback-content:active {
  background-color: #465eb8;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 0;
  margin-bottom: 25px;
  padding-bottom: 0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons::before, body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons::after {
  content: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons .float-left,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons .float-right {
  float: none;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a {
  background: inherit !important;
  color: inherit !important;
  border: 0;
  border-radius: 16px;
  padding: 5px 16px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 14px;
  line-height: 22px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 8px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a .fa-arrow-circle-right::before,
body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a .fa-arrow-circle-left::before {
  font-size: 16px;
  line-height: 22px;
  font-family: 'iconfont';
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a .fa-arrow-circle-left::before {
  content: '\e692';
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a .fa-arrow-circle-right::before {
  content: '\e68d';
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a:hover {
  background: #dfdfdf !important;
  color: #191919 !important;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .rst-footer-buttons a:focus {
  background: #aeaeae !important;
  color: #191919 !important;
  outline: 0;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-shadow: none;
          box-shadow: none;
  position: fixed;
  right: 30px;
  z-index: 3;
  top: 700px;
  gap: 16px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav .nav-item {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  -webkit-box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.08);
  background: #fff;
  display: flex;
  flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  align-items: center;
  padding: 12px;
  text-align: center;
  overflow: hidden;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav .nav-item .iconfont {
  color: #191919;
  font-size: 16px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav .nav-item .text {
  display: none;
  color: #191919;
  opacity: 0;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav .nav-item:hover {
  width: 72px;
  height: 56px;
  border-radius: 12px;
  background-color: #fff;
  padding: 8px 12px;
}

body .wy-grid-for-nav .wy-nav-content-wrap .wy-nav-content .rst-content footer .right-nav .nav-item:hover .text {
  opacity: 1;
  display: block;
}

.rst-content table.docutils:not(.field-list) tr:nth-child(2n-1) td,
.wy-table-backed,
.wy-table-odd td,
.wy-table-striped tr:nth-child(2n-1) td {
  background-color: rgba(223, 223, 223, 0.5);
}
/*# sourceMappingURL=custom.css.map */