/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 *
 * Description: Air Mouse RCU Keyscan Hold Process Header File. \n
 *
 * History: \n
 * 2024-09-27, Create file. \n
 */
#ifndef PROC_KEY_HOLD_H
#define PROC_KEY_HOLD_H

#include <stdint.h>
#include "../common/air_mouse_queue.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

void key_hold_process(msg_data_t *msg);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif
