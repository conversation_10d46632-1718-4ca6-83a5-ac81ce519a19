/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved. \n
 *
 * Description: msg adapter \n
 * Author:  \n
 * History: \n
 * 2023-11-10, Create file. \n
 */
#ifndef LOS_MEM_CHECK_H
#define LOS_MEM_CHECK_H

#include "los_base.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cpluscplus */
#endif /* __cpluscplus */

VOID LOS_PoolInfoGet(VOID *pool);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cpluscplus */
#endif /* __cpluscplus */

#endif
