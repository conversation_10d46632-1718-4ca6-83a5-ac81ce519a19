# 前言<a name="ZH-CN_TOPIC_0000001713102829"></a>

**概述<a name="section669mcpsimp"></a>**

BS2XV100通过API（Application Programming Interface）向开发者提供接入和使用星闪低功耗的相关接口，包括广播、连接以及SSAP服务注册、服务发现等，其他协议相关接口将在后续增量发布。

**产品版本<a name="section673mcpsimp"></a>**

与本文档对应的产品版本如下。

<a name="table676mcpsimp"></a>
<table><thead align="left"><tr id="row681mcpsimp"><th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.1"><p id="p683mcpsimp"><a name="p683mcpsimp"></a><a name="p683mcpsimp"></a><strong id="b684mcpsimp"><a name="b684mcpsimp"></a><a name="b684mcpsimp"></a>产品名称</strong></p>
</th>
<th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.2"><p id="p686mcpsimp"><a name="p686mcpsimp"></a><a name="p686mcpsimp"></a><strong id="b687mcpsimp"><a name="b687mcpsimp"></a><a name="b687mcpsimp"></a>产品版本</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row689mcpsimp"><td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.1 "><p id="p691mcpsimp"><a name="p691mcpsimp"></a><a name="p691mcpsimp"></a>BS2X</p>
</td>
<td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.2 "><p id="p693mcpsimp"><a name="p693mcpsimp"></a><a name="p693mcpsimp"></a>V100</p>
</td>
</tr>
</tbody>
</table>

**读者对象<a name="section694mcpsimp"></a>**

本文档主要适用以下工程师：

-   技术支持工程
-   软件开发工程师

**符号约定<a name="section133020216410"></a>**

在本文中可能出现下列标志，它们所代表的含义如下。

<a name="table2622507016410"></a>
<table><thead align="left"><tr id="row1530720816410"><th class="cellrowborder" valign="top" width="20.580000000000002%" id="mcps1.1.3.1.1"><p id="p6450074116410"><a name="p6450074116410"></a><a name="p6450074116410"></a><strong id="b2136615816410"><a name="b2136615816410"></a><a name="b2136615816410"></a>符号</strong></p>
</th>
<th class="cellrowborder" valign="top" width="79.42%" id="mcps1.1.3.1.2"><p id="p5435366816410"><a name="p5435366816410"></a><a name="p5435366816410"></a><strong id="b5941558116410"><a name="b5941558116410"></a><a name="b5941558116410"></a>说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row1372280416410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p3734547016410"><a name="p3734547016410"></a><a name="p3734547016410"></a><a name="image2670064316410"></a><a name="image2670064316410"></a><span><img class="" id="image2670064316410" src="figures/zh-cn_image_0000001665143038.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p1757432116410"><a name="p1757432116410"></a><a name="p1757432116410"></a>表示如不避免则将会导致死亡或严重伤害的具有高等级风险的危害。</p>
</td>
</tr>
<tr id="row466863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1432579516410"><a name="p1432579516410"></a><a name="p1432579516410"></a><a name="image4895582316410"></a><a name="image4895582316410"></a><span><img class="" id="image4895582316410" src="figures/zh-cn_image_0000001713022865.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p959197916410"><a name="p959197916410"></a><a name="p959197916410"></a>表示如不避免则可能导致死亡或严重伤害的具有中等级风险的危害。</p>
</td>
</tr>
<tr id="row123863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1232579516410"><a name="p1232579516410"></a><a name="p1232579516410"></a><a name="image1235582316410"></a><a name="image1235582316410"></a><span><img class="" id="image1235582316410" src="figures/zh-cn_image_0000001713102837.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p123197916410"><a name="p123197916410"></a><a name="p123197916410"></a>表示如不避免则可能导致轻微或中度伤害的具有低等级风险的危害。</p>
</td>
</tr>
<tr id="row5786682116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p2204984716410"><a name="p2204984716410"></a><a name="p2204984716410"></a><a name="image4504446716410"></a><a name="image4504446716410"></a><span><img class="" id="image4504446716410" src="figures/zh-cn_image_0000001665143034.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4388861916410"><a name="p4388861916410"></a><a name="p4388861916410"></a>用于传递设备或环境安全警示信息。如不避免则可能会导致设备损坏、数据丢失、设备性能降低或其它不可预知的结果。</p>
<p id="p1238861916410"><a name="p1238861916410"></a><a name="p1238861916410"></a>“须知”不涉及人身伤害。</p>
</td>
</tr>
<tr id="row2856923116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p5555360116410"><a name="p5555360116410"></a><a name="p5555360116410"></a><a name="image799324016410"></a><a name="image799324016410"></a><span><img class="" id="image799324016410" src="figures/zh-cn_image_0000001664983306.png" width="47.88" height="15.96"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4612588116410"><a name="p4612588116410"></a><a name="p4612588116410"></a>对正文中重点信息的补充说明。</p>
<p id="p1232588116410"><a name="p1232588116410"></a><a name="p1232588116410"></a>“说明”不是安全警示信息，不涉及人身、设备及环境伤害信息。</p>
</td>
</tr>
</tbody>
</table>

**修改记录<a name="section2467512116410"></a>**

<a name="table1557726816410"></a>
<table><thead align="left"><tr id="row2942532716410"><th class="cellrowborder" valign="top" width="19.11%" id="mcps1.1.4.1.1"><p id="p3778275416410"><a name="p3778275416410"></a><a name="p3778275416410"></a><strong id="b5687322716410"><a name="b5687322716410"></a><a name="b5687322716410"></a>文档版本</strong></p>
</th>
<th class="cellrowborder" valign="top" width="25.5%" id="mcps1.1.4.1.2"><p id="p5627845516410"><a name="p5627845516410"></a><a name="p5627845516410"></a><strong id="b5800814916410"><a name="b5800814916410"></a><a name="b5800814916410"></a>发布日期</strong></p>
</th>
<th class="cellrowborder" valign="top" width="55.38999999999999%" id="mcps1.1.4.1.3"><p id="p2382284816410"><a name="p2382284816410"></a><a name="p2382284816410"></a><strong id="b3316380216410"><a name="b3316380216410"></a><a name="b3316380216410"></a>修改说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row139211916143017"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p1192214161301"><a name="p1192214161301"></a><a name="p1192214161301"></a>05</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p1092221618304"><a name="p1092221618304"></a><a name="p1092221618304"></a>2025-05-30</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p19940110162410"><a name="p19940110162410"></a><a name="p19940110162410"></a>更新“<a href="错误码.md">错误码</a>”小节内容。</p>
</td>
</tr>
<tr id="row571052994013"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p5710729184020"><a name="p5710729184020"></a><a name="p5710729184020"></a>04</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p1471052911409"><a name="p1471052911409"></a><a name="p1471052911409"></a>2025-01-24</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p171011299406"><a name="p171011299406"></a><a name="p171011299406"></a>新增“<a href="SLE-Passkey接口.md">SLE Passkey接口</a>”章节内容。</p>
</td>
</tr>
<tr id="row137112470231"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p137194742319"><a name="p137194742319"></a><a name="p137194742319"></a>03</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p137115478238"><a name="p137115478238"></a><a name="p137115478238"></a>2024-09-13</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><a name="ul10871154016303"></a><a name="ul10871154016303"></a><ul id="ul10871154016303"><li>更新“<a href="错误码.md">错误码</a>”小节内容。</li><li>更新“<a href="Connection-Manager接口.md">Connection Manager接口</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</li><li>更新“<a href="SSAP-Server接口.md">SSAP Server接口</a>”的“<a href="开发流程-4.md">开发流程</a>”小节内容。</li></ul>
</td>
</tr>
<tr id="row310415618442"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p4105145618442"><a name="p4105145618442"></a><a name="p4105145618442"></a>02</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p15105656134416"><a name="p15105656134416"></a><a name="p15105656134416"></a>2024-08-02</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p740117684512"><a name="p740117684512"></a><a name="p740117684512"></a>更新“<a href="Connection-Manager接口.md">Connection Manager接口</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</p>
</td>
</tr>
<tr id="row143689493249"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p73696492240"><a name="p73696492240"></a><a name="p73696492240"></a>01</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p173691498244"><a name="p173691498244"></a><a name="p173691498244"></a>2024-05-15</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p169114261115"><a name="p169114261115"></a><a name="p169114261115"></a>第一次正式版本发布。</p>
<p id="p73691549112415"><a name="p73691549112415"></a><a name="p73691549112415"></a>新增“<a href="Transmission-Manager接口.md">Transmission Manager接口</a>”章节内容。</p>
</td>
</tr>
<tr id="row10827102717595"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p1982722715914"><a name="p1982722715914"></a><a name="p1982722715914"></a>00B04</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p4827102715592"><a name="p4827102715592"></a><a name="p4827102715592"></a>2024-04-25</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p118271727165918"><a name="p118271727165918"></a><a name="p118271727165918"></a>更新“<a href="Connection-Manager接口.md">Connection Manager接口</a>”章节内容。</p>
</td>
</tr>
<tr id="row1732717673314"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p133273643313"><a name="p133273643313"></a><a name="p133273643313"></a>00B03</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p83273615337"><a name="p83273615337"></a><a name="p83273615337"></a>2024-01-08</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p1932717610335"><a name="p1932717610335"></a><a name="p1932717610335"></a>更新“<a href="Device-Discovery接口.md">Device Discovery接口</a>”的“<a href="注意事项.md">注意事项</a>”小节内容。</p>
</td>
</tr>
<tr id="row9154161512215"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p191548156218"><a name="p191548156218"></a><a name="p191548156218"></a>00B02</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p13154191512210"><a name="p13154191512210"></a><a name="p13154191512210"></a>2023-12-08</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p92511307219"><a name="p92511307219"></a><a name="p92511307219"></a>更新“<a href="Connection-Manager接口.md">Connection Manager接口</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</p>
</td>
</tr>
<tr id="row5947359616410"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p2149706016410"><a name="p2149706016410"></a><a name="p2149706016410"></a>00B01</p>
</td>
<td class="cellrowborder" valign="top" width="25.5%" headers="mcps1.1.4.1.2 "><p id="p648803616410"><a name="p648803616410"></a><a name="p648803616410"></a>2023-09-27</p>
</td>
<td class="cellrowborder" valign="top" width="55.38999999999999%" headers="mcps1.1.4.1.3 "><p id="p1946537916410"><a name="p1946537916410"></a><a name="p1946537916410"></a>第一次临时版本发布。</p>
</td>
</tr>
</tbody>
</table>

# 概述<a name="ZH-CN_TOPIC_0000001664983290"></a>

BS2XV100通过API（Application Programming Interface）面向开发者提供SLE功能的开发和应用接口，包括Device Discovery, Connection Manager, SSAP等。

各组件功能说明如下：

-   Device Discovery：星闪设备发现协议，包括设备管理、设备公开和设备发现接口。
-   Connection Manager：星闪连接管理协议，包括设备连接、配对相关接口。
-   SSAP：星闪服务交互协议（SparkLink Service Access Protocol），包含服务注册、服务发现、属性数据读写等功能相关接口。
-   Low Latency：低时延初始化和低时延数据收发接口。

>![](public_sys-resources/icon-note.gif) **说明：** 
>该文档描述各个模块功能的基本流程和API接口描述。


## 错误码<a name="ZH-CN_TOPIC_0000001664983298"></a>

SLE SDK使用错误码指示用户当前任务执行结果，如[表1](#table9501182016504)所示。

**表 1**  错误码

<a name="table9501182016504"></a>
<table><thead align="left"><tr id="row950292085010"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p25029205503"><a name="p25029205503"></a><a name="p25029205503"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="34.88%" id="mcps1.*******"><p id="p1350272085016"><a name="p1350272085016"></a><a name="p1350272085016"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="19.66%" id="mcps1.*******"><p id="p19502152005012"><a name="p19502152005012"></a><a name="p19502152005012"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="36.46%" id="mcps1.*******"><p id="p950262016502"><a name="p950262016502"></a><a name="p950262016502"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row10502132020509"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p750232017508"><a name="p750232017508"></a><a name="p750232017508"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p9507184684016"><a name="p9507184684016"></a><a name="p9507184684016"></a>ERRCODE_SLE_SUCCESS</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1350242035012"><a name="p1350242035012"></a><a name="p1350242035012"></a>0</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p2478152273213"><a name="p2478152273213"></a><a name="p2478152273213"></a>执行成功错误码。</p>
</td>
</tr>
<tr id="row950211209505"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p75021320195017"><a name="p75021320195017"></a><a name="p75021320195017"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p20333147191410"><a name="p20333147191410"></a><a name="p20333147191410"></a>ERRCODE_SLE_CONTINUE</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p206202225145"><a name="p206202225145"></a><a name="p206202225145"></a>0x80006000</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p2580741135211"><a name="p2580741135211"></a><a name="p2580741135211"></a>继续执行错误码。</p>
</td>
</tr>
<tr id="row647004635216"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p9470164695211"><a name="p9470164695211"></a><a name="p9470164695211"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p13407175741418"><a name="p13407175741418"></a><a name="p13407175741418"></a>ERRCODE_SLE_DIRECT_RETURN</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1247054675220"><a name="p1247054675220"></a><a name="p1247054675220"></a>0x80006001</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p349816514539"><a name="p349816514539"></a><a name="p349816514539"></a>直接返回错误码。</p>
</td>
</tr>
<tr id="row1046837194414"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p362492722119"><a name="p362492722119"></a><a name="p362492722119"></a>4</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p846115146226"><a name="p846115146226"></a><a name="p846115146226"></a>ERRCODE_SLE_NO_ATTATION</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1441442142218"><a name="p1441442142218"></a><a name="p1441442142218"></a>0x80006002</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p462417274215"><a name="p462417274215"></a><a name="p462417274215"></a>-</p>
</td>
</tr>
<tr id="row208504495415"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p485544155412"><a name="p485544155412"></a><a name="p485544155412"></a>5</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p3642109201615"><a name="p3642109201615"></a><a name="p3642109201615"></a>ERRCODE_SLE_PARAM_ERR</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p202639558212"><a name="p202639558212"></a><a name="p202639558212"></a>0x80006003</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p107082179351"><a name="p107082179351"></a><a name="p107082179351"></a>参数错误错误码。</p>
</td>
</tr>
<tr id="row2914115711542"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p15914145716545"><a name="p15914145716545"></a><a name="p15914145716545"></a>6</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p93391714121614"><a name="p93391714121614"></a><a name="p93391714121614"></a>ERRCODE_SLE_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p061233385012"><a name="p061233385012"></a><a name="p061233385012"></a>0x80006004</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p925873013511"><a name="p925873013511"></a><a name="p925873013511"></a>执行失败错误码。</p>
</td>
</tr>
<tr id="row13726122553"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p187261621559"><a name="p187261621559"></a><a name="p187261621559"></a>7</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p331471919167"><a name="p331471919167"></a><a name="p331471919167"></a>ERRCODE_SLE_TIMEOUT</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1991455717548"><a name="p1991455717548"></a><a name="p1991455717548"></a>0x80006005</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p1650784303513"><a name="p1650784303513"></a><a name="p1650784303513"></a>执行超时错误码。</p>
</td>
</tr>
<tr id="row119961302554"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1799683013557"><a name="p1799683013557"></a><a name="p1799683013557"></a>8</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p135588237160"><a name="p135588237160"></a><a name="p135588237160"></a>ERRCODE_SLE_UNSUPPORTED</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p10543203162210"><a name="p10543203162210"></a><a name="p10543203162210"></a>0x80006006</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p795215509352"><a name="p795215509352"></a><a name="p795215509352"></a>参数不支持错误码。</p>
</td>
</tr>
<tr id="row121607340559"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p4160934185512"><a name="p4160934185512"></a><a name="p4160934185512"></a>9</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p18130228161619"><a name="p18130228161619"></a><a name="p18130228161619"></a>ERRCODE_SLE_GETRECORD_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p19961530125516"><a name="p19961530125516"></a><a name="p19961530125516"></a>0x80006007</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p03951456183511"><a name="p03951456183511"></a><a name="p03951456183511"></a>获取当前记录失败错误码。</p>
</td>
</tr>
<tr id="row13618338115517"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3618103810553"><a name="p3618103810553"></a><a name="p3618103810553"></a>10</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1577345515167"><a name="p1577345515167"></a><a name="p1577345515167"></a>ERRCODE_SLE_POINTER_NULL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p196909199227"><a name="p196909199227"></a><a name="p196909199227"></a>0x80006008</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p133310414365"><a name="p133310414365"></a><a name="p133310414365"></a>指针为空错误码。</p>
</td>
</tr>
<tr id="row179331321145612"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1793472111566"><a name="p1793472111566"></a><a name="p1793472111566"></a>11</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p2476909178"><a name="p2476909178"></a><a name="p2476909178"></a>ERRCODE_SLE_NO_RECORD</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p761819387554"><a name="p761819387554"></a><a name="p761819387554"></a>0x80006009</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p1738191073619"><a name="p1738191073619"></a><a name="p1738191073619"></a>无记录返回错误码。</p>
</td>
</tr>
<tr id="row24341235135618"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3434635155618"><a name="p3434635155618"></a><a name="p3434635155618"></a>12</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p132156631714"><a name="p132156631714"></a><a name="p132156631714"></a>ERRCODE_SLE_STATUS_ERR</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1934132112566"><a name="p1934132112566"></a><a name="p1934132112566"></a>0x8000600a</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p154731715123613"><a name="p154731715123613"></a><a name="p154731715123613"></a>状态错误错误码。</p>
</td>
</tr>
<tr id="row625216013410"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p12252200193410"><a name="p12252200193410"></a><a name="p12252200193410"></a>13</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p15538141113177"><a name="p15538141113177"></a><a name="p15538141113177"></a>ERRCODE_SLE_NOMEM</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p15434193511569"><a name="p15434193511569"></a><a name="p15434193511569"></a>0x8000600b</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p20720192013616"><a name="p20720192013616"></a><a name="p20720192013616"></a>内存不足错误码。</p>
</td>
</tr>
<tr id="row22071149346"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p132076412341"><a name="p132076412341"></a><a name="p132076412341"></a>14</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1931217162176"><a name="p1931217162176"></a><a name="p1931217162176"></a>ERRCODE_SLE_AUTH_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1425210017341"><a name="p1425210017341"></a><a name="p1425210017341"></a>0x8000600c</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p14652182523613"><a name="p14652182523613"></a><a name="p14652182523613"></a>认证失败错误码。</p>
</td>
</tr>
<tr id="row19824101991719"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p15824111911170"><a name="p15824111911170"></a><a name="p15824111911170"></a>15</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p72741534161715"><a name="p72741534161715"></a><a name="p72741534161715"></a>ERRCODE_SLE_AUTH_PKEY_MISS</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p182072044349"><a name="p182072044349"></a><a name="p182072044349"></a>0x8000600d</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p24881812275"><a name="p24881812275"></a><a name="p24881812275"></a>PIN码或密钥丢失致认证失败错误码。</p>
</td>
</tr>
<tr id="row1812615238178"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p171261623181716"><a name="p171261623181716"></a><a name="p171261623181716"></a>16</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p10147146161716"><a name="p10147146161716"></a><a name="p10147146161716"></a>ERRCODE_SLE_RMT_DEV_DOWN</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p48241119151715"><a name="p48241119151715"></a><a name="p48241119151715"></a>0x8000600e</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p4126132321714"><a name="p4126132321714"></a><a name="p4126132321714"></a>对端设备关闭错误码。</p>
</td>
</tr>
<tr id="row128112720177"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p18811527151714"><a name="p18811527151714"></a><a name="p18811527151714"></a>17</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1993165071710"><a name="p1993165071710"></a><a name="p1993165071710"></a>ERRCODE_SLE_PAIRING_REJECT</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1812620235173"><a name="p1812620235173"></a><a name="p1812620235173"></a>0x8000600f</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p4811270173"><a name="p4811270173"></a><a name="p4811270173"></a>配对拒绝错误码。</p>
</td>
</tr>
<tr id="row1674155911712"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p4674059151713"><a name="p4674059151713"></a><a name="p4674059151713"></a>18</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1437613961814"><a name="p1437613961814"></a><a name="p1437613961814"></a>ERRCODE_SLE_BUSY</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p78116279173"><a name="p78116279173"></a><a name="p78116279173"></a>0x80006010</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p1674135911170"><a name="p1674135911170"></a><a name="p1674135911170"></a>系统繁忙错误码。</p>
</td>
</tr>
<tr id="row895417313189"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1995483101815"><a name="p1995483101815"></a><a name="p1995483101815"></a>19</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1878418136183"><a name="p1878418136183"></a><a name="p1878418136183"></a>ERRCODE_SLE_NOT_READY</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p5674105917175"><a name="p5674105917175"></a><a name="p5674105917175"></a>0x80006011</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p29543316189"><a name="p29543316189"></a><a name="p29543316189"></a>系统未准备好错误码。</p>
</td>
</tr>
<tr id="row536982318185"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p18369152311813"><a name="p18369152311813"></a><a name="p18369152311813"></a>20</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p195253018185"><a name="p195253018185"></a><a name="p195253018185"></a>ERRCODE_SLE_CONN_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p795410311182"><a name="p795410311182"></a><a name="p795410311182"></a>0x80006012</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p1936912310184"><a name="p1936912310184"></a><a name="p1936912310184"></a>连接失败错误码。</p>
</td>
</tr>
<tr id="row571652611817"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p371620260187"><a name="p371620260187"></a><a name="p371620260187"></a>21</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p16264731151913"><a name="p16264731151913"></a><a name="p16264731151913"></a>ERRCODE_SLE_OUT_OF_RANGE</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p173691123191815"><a name="p173691123191815"></a><a name="p173691123191815"></a>0x80006013</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p271610264181"><a name="p271610264181"></a><a name="p271610264181"></a>越界错误码。</p>
</td>
</tr>
<tr id="row19347112020180"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p18347162091810"><a name="p18347162091810"></a><a name="p18347162091810"></a>22</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p3352113510199"><a name="p3352113510199"></a><a name="p3352113510199"></a>ERRCODE_SLE_MEMCPY_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p1271652691818"><a name="p1271652691818"></a><a name="p1271652691818"></a>0x80006014</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p19347720131811"><a name="p19347720131811"></a><a name="p19347720131811"></a>拷贝失败错误码。</p>
</td>
</tr>
<tr id="row118631840181910"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p186334011198"><a name="p186334011198"></a><a name="p186334011198"></a>23</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p121431546161911"><a name="p121431546161911"></a><a name="p121431546161911"></a>ERRCODE_SLE_MALLOC_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.66%" headers="mcps1.******* "><p id="p63471208184"><a name="p63471208184"></a><a name="p63471208184"></a>0x80006015</p>
</td>
<td class="cellrowborder" valign="top" width="36.46%" headers="mcps1.******* "><p id="p2863740111910"><a name="p2863740111910"></a><a name="p2863740111910"></a>内存申请失败错误码。</p>
</td>
</tr>
</tbody>
</table>

**断连原因<a name="section34341452163217"></a>**

异常断连原因如[表2](#table1546843103312)所示。

**表 2**  断连原因

<a name="table1546843103312"></a>
<table><thead align="left"><tr id="row9467437331"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p1846184317333"><a name="p1846184317333"></a><a name="p1846184317333"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="34.88%" id="mcps1.*******"><p id="p046184333312"><a name="p046184333312"></a><a name="p046184333312"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="19.68%" id="mcps1.*******"><p id="p13461343153310"><a name="p13461343153310"></a><a name="p13461343153310"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="36.44%" id="mcps1.*******"><p id="p14467437333"><a name="p14467437333"></a><a name="p14467437333"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row1461443153319"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p546143173311"><a name="p546143173311"></a><a name="p546143173311"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p2461843113319"><a name="p2461843113319"></a><a name="p2461843113319"></a>SLE_DISCONNECT_UNKNOWN</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p1446154333311"><a name="p1446154333311"></a><a name="p1446154333311"></a>0x00</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p20982113092610"><a name="p20982113092610"></a><a name="p20982113092610"></a>未知原因断连。</p>
</td>
</tr>
<tr id="row184614393319"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p17461443123320"><a name="p17461443123320"></a><a name="p17461443123320"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p12778145183516"><a name="p12778145183516"></a><a name="p12778145183516"></a>SLE_DISCONNECT_BY_PIN_OR_KEY_MISSING</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p18461643133316"><a name="p18461643133316"></a><a name="p18461643133316"></a>0x05</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p898293032612"><a name="p898293032612"></a><a name="p898293032612"></a>pin或key丢失导致断连。</p>
</td>
</tr>
<tr id="row18461543163319"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1247184316339"><a name="p1247184316339"></a><a name="p1247184316339"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p97741683516"><a name="p97741683516"></a><a name="p97741683516"></a>SLE_DISCONNECT_BY_CONNECT_TIMEOUT</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p44794317338"><a name="p44794317338"></a><a name="p44794317338"></a>0x07</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p10982230192617"><a name="p10982230192617"></a><a name="p10982230192617"></a>连接超时断连。</p>
</td>
</tr>
<tr id="row174794383315"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p2047164315331"><a name="p2047164315331"></a><a name="p2047164315331"></a>5</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p168606266359"><a name="p168606266359"></a><a name="p168606266359"></a>SLE_D ISCONNECT_BY_REMOTE_USER</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p2474433332"><a name="p2474433332"></a><a name="p2474433332"></a>0x10</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1198243017265"><a name="p1198243017265"></a><a name="p1198243017265"></a>远端用户断连。</p>
</td>
</tr>
<tr id="row547164393311"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p24754318339"><a name="p24754318339"></a><a name="p24754318339"></a>6</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p72781839153514"><a name="p72781839153514"></a><a name="p72781839153514"></a>SLE_DISCONNECT_BY_LOCAL_HOST</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p19471443173315"><a name="p19471443173315"></a><a name="p19471443173315"></a>0x11</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1598311305260"><a name="p1598311305260"></a><a name="p1598311305260"></a>本端host断连。</p>
</td>
</tr>
<tr id="row194714312335"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p18471743153315"><a name="p18471743153315"></a><a name="p18471743153315"></a>7</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p1652713565357"><a name="p1652713565357"></a><a name="p1652713565357"></a>SLE_DISCONNECT_BY_MIC_ERROR</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p4471543173315"><a name="p4471543173315"></a><a name="p4471543173315"></a>0x1B</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1798318305261"><a name="p1798318305261"></a><a name="p1798318305261"></a>MIC error断连。</p>
</td>
</tr>
<tr id="row746817284367"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1946802819369"><a name="p1946802819369"></a><a name="p1946802819369"></a>8</p>
</td>
<td class="cellrowborder" valign="top" width="34.88%" headers="mcps1.******* "><p id="p164681328193615"><a name="p164681328193615"></a><a name="p164681328193615"></a>SLE_ESTABLISH_CONNECT_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="19.68%" headers="mcps1.******* "><p id="p124681728203610"><a name="p124681728203610"></a><a name="p124681728203610"></a>0x1C</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p798313082616"><a name="p798313082616"></a><a name="p798313082616"></a>建连异常。</p>
</td>
</tr>
</tbody>
</table>

# Device Discovery接口<a name="ZH-CN_TOPIC_0000001713022861"></a>




## 概述<a name="ZH-CN_TOPIC_0000001713102833"></a>

Device Discovery接口是星闪设备发现协议的软件实现，主要功能有SLE设备开关、设备管理、设备公开和设备发现。

## 开发流程<a name="ZH-CN_TOPIC_0000001713102825"></a>

**使用场景<a name="section775mcpsimp"></a>**

打开SLE设备开关是使用SLE功能的首要条件，SLE启动后可进行设备信息管理，包括获取与设置本地设备名称、获取与设置本地设备地址和设置本地设备外观。

-   当SLE设备需要进行设备公开时，可先设置设备公开参数、设备公开数据，然后使能设备公开。
-   当SLE设备需要进行设备发现时，可先设置设备发现参数，然后使能设备发现，并通过回调函数观察发现到的设备公开数据包。

**功能<a name="section779mcpsimp"></a>**

Device Discovery提供的接口如[表1](#_table213321716161)所示。

**表 1**  Device Discovery接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="22.63%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="21.790000000000003%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="24.9%" id="mcps1.*******"><p id="p142843230222"><a name="p142843230222"></a><a name="p142843230222"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="30.680000000000003%" id="mcps1.*******"><p id="p79178221244"><a name="p79178221244"></a><a name="p79178221244"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row154823161515"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1724715716352"><a name="p1724715716352"></a><a name="p1724715716352"></a>enable_sle</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p15485312159"><a name="p15485312159"></a><a name="p15485312159"></a>使能SLE。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p1428412233222"><a name="p1428412233222"></a><a name="p1428412233222"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p998181701718"><a name="p998181701718"></a><a name="p998181701718"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row43102035171512"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p2352131733519"><a name="p2352131733519"></a><a name="p2352131733519"></a>disable_sle</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p183118354157"><a name="p183118354157"></a><a name="p183118354157"></a>去使能SLE。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p0284723122215"><a name="p0284723122215"></a><a name="p0284723122215"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p134171515151711"><a name="p134171515151711"></a><a name="p134171515151711"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row106094013183"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p77391225163517"><a name="p77391225163517"></a><a name="p77391225163517"></a>sle_set_local_addr</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p1860740181818"><a name="p1860740181818"></a><a name="p1860740181818"></a>设置本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p112562015192311"><a name="p112562015192311"></a><a name="p112562015192311"></a>addr：本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p3320171314172"><a name="p3320171314172"></a><a name="p3320171314172"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row804mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p188373342352"><a name="p188373342352"></a><a name="p188373342352"></a>sle_get_local_addr</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p808mcpsimp"><a name="p808mcpsimp"></a><a name="p808mcpsimp"></a>获取本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p3890283569"><a name="p3890283569"></a><a name="p3890283569"></a>addr：[out]本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p5422117171"><a name="p5422117171"></a><a name="p5422117171"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p975517417353"><a name="p975517417353"></a><a name="p975517417353"></a>sle_set_local_name</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>设置本地设备名称。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p8164154025813"><a name="p8164154025813"></a><a name="p8164154025813"></a>name：本地设备名称；</p>
<p id="p9164103612431"><a name="p9164103612431"></a><a name="p9164103612431"></a>len：本地设备名称长度。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p11816872176"><a name="p11816872176"></a><a name="p11816872176"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row814mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1664834783519"><a name="p1664834783519"></a><a name="p1664834783519"></a>sle_get_local_name</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p818mcpsimp"><a name="p818mcpsimp"></a><a name="p818mcpsimp"></a>获取本地设备名称。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p1486315125215"><a name="p1486315125215"></a><a name="p1486315125215"></a>name：[out]本地设备名称；</p>
<p id="p128631251135216"><a name="p128631251135216"></a><a name="p128631251135216"></a>len：[inout]入参时为用户预留内存大小，出参时为本地设备名称长度。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p4301769174"><a name="p4301769174"></a><a name="p4301769174"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row188161146102212"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p877916595358"><a name="p877916595358"></a><a name="p877916595358"></a>sle_set_announce_data</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p118162046122215"><a name="p118162046122215"></a><a name="p118162046122215"></a>设置设备公开数据。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p12284132313221"><a name="p12284132313221"></a><a name="p12284132313221"></a>announce_id：设备公开ID；</p>
<p id="p7389135819533"><a name="p7389135819533"></a><a name="p7389135819533"></a>data：设备公开数据。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p748344111158"><a name="p748344111158"></a><a name="p748344111158"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row112552812172"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p25051618369"><a name="p25051618369"></a><a name="p25051618369"></a>sle_set_announce_param</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p6125628121712"><a name="p6125628121712"></a><a name="p6125628121712"></a>设置设备公开参数。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p332162685418"><a name="p332162685418"></a><a name="p332162685418"></a>announce_id：设备公开ID；</p>
<p id="p10321182618547"><a name="p10321182618547"></a><a name="p10321182618547"></a>data：设备公开参数。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p159918479188"><a name="p159918479188"></a><a name="p159918479188"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row2075810103373"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p19536161717375"><a name="p19536161717375"></a><a name="p19536161717375"></a>sle_start_announce</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p197582104377"><a name="p197582104377"></a><a name="p197582104377"></a>开始设备公开。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p2075812100371"><a name="p2075812100371"></a><a name="p2075812100371"></a>announce_id：设备公开ID。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p167587105376"><a name="p167587105376"></a><a name="p167587105376"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row196751322103710"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p19806103717375"><a name="p19806103717375"></a><a name="p19806103717375"></a>sle_stop_announce</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p46751022163713"><a name="p46751022163713"></a><a name="p46751022163713"></a>停止设备公开。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p1367552215371"><a name="p1367552215371"></a><a name="p1367552215371"></a>announce_id：设备公开ID。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p1667522218378"><a name="p1667522218378"></a><a name="p1667522218378"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row8699192713372"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p7329204913718"><a name="p7329204913718"></a><a name="p7329204913718"></a>sle_set_seek_param</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p1369914272379"><a name="p1369914272379"></a><a name="p1369914272379"></a>设置设备发现参数。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p66991272372"><a name="p66991272372"></a><a name="p66991272372"></a>param：设备发现参数。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p13699142743713"><a name="p13699142743713"></a><a name="p13699142743713"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row12968154314372"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p13444545370"><a name="p13444545370"></a><a name="p13444545370"></a>sle_start_seek</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p1596854318376"><a name="p1596854318376"></a><a name="p1596854318376"></a>开始设备发现。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p13968194333715"><a name="p13968194333715"></a><a name="p13968194333715"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p10968174312379"><a name="p10968174312379"></a><a name="p10968174312379"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row0662155743714"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p41823312384"><a name="p41823312384"></a><a name="p41823312384"></a>sle_stop_seek</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p18662125711376"><a name="p18662125711376"></a><a name="p18662125711376"></a>停止设备发现。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p2662135793717"><a name="p2662135793717"></a><a name="p2662135793717"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p196621157153719"><a name="p196621157153719"></a><a name="p196621157153719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row196417663815"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p145631020383"><a name="p145631020383"></a><a name="p145631020383"></a>sle_announce_seek_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="21.790000000000003%" headers="mcps1.******* "><p id="p10649623811"><a name="p10649623811"></a><a name="p10649623811"></a>注册设备公开和设备发现回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="24.9%" headers="mcps1.******* "><p id="p176416633819"><a name="p176416633819"></a><a name="p176416633819"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="30.680000000000003%" headers="mcps1.******* "><p id="p1364176193812"><a name="p1364176193812"></a><a name="p1364176193812"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

Device Discovery开发的典型流程如下，具体编程实例可参考application/samples/bt。

**Terminal Node：**

1.  调用enable\_sle，打开SLE开关。
2.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
3.  调用sle\_set\_local\_addr，设置本地设备地址。
4.  调用sle\_set\_local\_name，设置本地设备名称。
5.  调用sle\_set\_announce\_param，设置设备公开参数
6.  调用sle\_set\_announce\_data，设置设备公开数据
7.  调用sle\_start\_announce，启动设备公开。

**Grant Node：**

1.  调用enable\_sle，打开SLE开关。
2.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
3.  调用sle\_set\_local\_addr，设置本地设备地址。
4.  调用sle\_set\_local\_name，设置本地设备名称。
5.  调用sle\_set\_seek\_param，设置设备发现参数。
6.  调用sle\_start\_seek，启动设备发现，并在回调函数中获得正在进行设备公开的设备信息。

## 注意事项<a name="ZH-CN_TOPIC_0000001665143026"></a>

-   BS2XV100产品只作为SLE设备工作时，最多只支持八路连接，同时作为BLE和SLE设备工作时，一共支持八路连接。
-   若扫描不到设备，请先检查设备是否已在配对设备列表中，或者设备是否已与其他设备配对（此情况下需要先清除设备端配对信息）。

# Connection Manager接口<a name="ZH-CN_TOPIC_0000001713022853"></a>



## 概述<a name="ZH-CN_TOPIC_0000001713022857"></a>

Connection Manager接口是星闪连接管理协议的软件实现，主要功能有连接、配对和读远端设备RSSI值。

## 开发流程<a name="ZH-CN_TOPIC_0000001664983294"></a>

**使用场景<a name="section775mcpsimp"></a>**

当设备需要与对端设备建立连接时，可向对端设备发起连接请求。在连接过程中，设备可读取远端设备RSSI值，当设备需要更新连接参数时，可向对端设备发起连接参数更新请求，当设备需要与对端设备配对时，可向对端设备发起配对请求。在配对过程中，可获取当前本端设备与指定对端设备的配对状态。设备可获取当前配对设备数量以及当前配对设备信息链表，当前的链路角色。

**功能<a name="section779mcpsimp"></a>**

Connection Manager提供的接口如[表1](#_table213321716161)所示。

**表 1**  Connection Manager接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="20.42204220422042%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="28.63286328632863%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.58275827582758%" id="mcps1.*******"><p id="p134231719219"><a name="p134231719219"></a><a name="p134231719219"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p109931320124"><a name="p109931320124"></a><a name="p109931320124"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p19908144365315"><a name="p19908144365315"></a><a name="p19908144365315"></a>sle_connect_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p8737184118442"><a name="p8737184118442"></a><a name="p8737184118442"></a>向对端设备发起连接请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p382884718520"><a name="p382884718520"></a><a name="p382884718520"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1399310205210"><a name="p1399310205210"></a><a name="p1399310205210"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p6917145014531"><a name="p6917145014531"></a><a name="p6917145014531"></a>sle_disconnect_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p62365161575"><a name="p62365161575"></a><a name="p62365161575"></a>向对端设备发起断连请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p331519572913"><a name="p331519572913"></a><a name="p331519572913"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p3262125685315"><a name="p3262125685315"></a><a name="p3262125685315"></a>sle_update_connect_param</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>连接参数更新。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p8315175717913"><a name="p8315175717913"></a><a name="p8315175717913"></a>params：连接参数</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p580165015586"><a name="p580165015586"></a><a name="p580165015586"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row14457165322015"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p172211115541"><a name="p172211115541"></a><a name="p172211115541"></a>sle_pair_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p84448314816"><a name="p84448314816"></a><a name="p84448314816"></a>向对端设备发起配对请求。</p>
<p id="p5457753102011"><a name="p5457753102011"></a><a name="p5457753102011"></a>（目前星闪鉴权流程仅支持免输入模式）</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p7314165718918"><a name="p7314165718918"></a><a name="p7314165718918"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1321275215811"><a name="p1321275215811"></a><a name="p1321275215811"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p10492196135420"><a name="p10492196135420"></a><a name="p10492196135420"></a>sle_remove_paired_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p828mcpsimp"><a name="p828mcpsimp"></a><a name="p828mcpsimp"></a>与对端设备取消配对。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p1831314574916"><a name="p1831314574916"></a><a name="p1831314574916"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1699320201023"><a name="p1699320201023"></a><a name="p1699320201023"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1257712474513"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p13599712175418"><a name="p13599712175418"></a><a name="p13599712175418"></a>sle_remove_all_pairs</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p13185839205810"><a name="p13185839205810"></a><a name="p13185839205810"></a>取消与所有对端设备的配对。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p83125579911"><a name="p83125579911"></a><a name="p83125579911"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p15960115591011"><a name="p15960115591011"></a><a name="p15960115591011"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row386531016"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p1698101810547"><a name="p1698101810547"></a><a name="p1698101810547"></a>sle_get_paired_devices_num</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p5732114055213"><a name="p5732114055213"></a><a name="p5732114055213"></a>获取配对设备数量。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p33108571497"><a name="p33108571497"></a><a name="p33108571497"></a>number：[out]配对设备数量。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1315723515115"><a name="p1315723515115"></a><a name="p1315723515115"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1913210313121"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p6481323205412"><a name="p6481323205412"></a><a name="p6481323205412"></a>sle_get_paired_devices</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p1180311441527"><a name="p1180311441527"></a><a name="p1180311441527"></a>获取配对设备信息。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p83095571597"><a name="p83095571597"></a><a name="p83095571597"></a>addr：[out]设备地址链表；</p>
<p id="p186945298114"><a name="p186945298114"></a><a name="p186945298114"></a>number：[inout]入参时为用户预留内存大小，出参时为设备数量。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1570763941415"><a name="p1570763941415"></a><a name="p1570763941415"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row829mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p85150315548"><a name="p85150315548"></a><a name="p85150315548"></a>sle_get_pair_state</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p833mcpsimp"><a name="p833mcpsimp"></a><a name="p833mcpsimp"></a>获取配对状态。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p14308557694"><a name="p14308557694"></a><a name="p14308557694"></a>addr：设备地址；</p>
<p id="p1792882011123"><a name="p1792882011123"></a><a name="p1792882011123"></a>state：[out]配对状态。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p7174165817582"><a name="p7174165817582"></a><a name="p7174165817582"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1549875915535"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p19498259125317"><a name="p19498259125317"></a><a name="p19498259125317"></a>sle_get_connect_role</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p1498115914537"><a name="p1498115914537"></a><a name="p1498115914537"></a>获取链路角色。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p3498115910535"><a name="p3498115910535"></a><a name="p3498115910535"></a>conn_id: 连接id；</p>
<p id="p12261128564"><a name="p12261128564"></a><a name="p12261128564"></a>role: [out]链路角色。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p18498125955310"><a name="p18498125955310"></a><a name="p18498125955310"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row834mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p141701237145412"><a name="p141701237145412"></a><a name="p141701237145412"></a>sle_read_remote_device_rssi</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p838mcpsimp"><a name="p838mcpsimp"></a><a name="p838mcpsimp"></a>读对端设备RSSI值。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p230710570918"><a name="p230710570918"></a><a name="p230710570918"></a>conn_id：连接id。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p63401159185813"><a name="p63401159185813"></a><a name="p63401159185813"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row960313012613"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p3603430102617"><a name="p3603430102617"></a><a name="p3603430102617"></a>sle_set_phy_param</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p3603163062611"><a name="p3603163062611"></a><a name="p3603163062611"></a>更新星闪phy参数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p155671750104310"><a name="p155671750104310"></a><a name="p155671750104310"></a>conn_id：连接id;</p>
<p id="p722253520331"><a name="p722253520331"></a><a name="p722253520331"></a>param：phy更新的配置参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1260410302263"><a name="p1260410302263"></a><a name="p1260410302263"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row844mcpsimp"><td class="cellrowborder" valign="top" width="20.42204220422042%" headers="mcps1.******* "><p id="p17554124365419"><a name="p17554124365419"></a><a name="p17554124365419"></a>sle_connection_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="28.63286328632863%" headers="mcps1.******* "><p id="p1230216577176"><a name="p1230216577176"></a><a name="p1230216577176"></a>注册连接管理回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.58275827582758%" headers="mcps1.******* "><p id="p230716571497"><a name="p230716571497"></a><a name="p230716571497"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p17545120155913"><a name="p17545120155913"></a><a name="p17545120155913"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

Connection Manager开发的典型流程如下，具体编程实例可参考application/samples/bt。

**Terminal Node：**

1.  调用enable\_sle，打开SLE开关。
2.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
3.  调用sle\_connection\_register\_callbacks，注册连接管理回调函数。
4.  调用sle\_set\_local\_addr，设置本地设备地址。
5.  调用sle\_set\_local\_name，设置本地设备名称。
6.  调用sle\_set\_announce\_param，设置设备公开参数
7.  调用sle\_set\_announce\_data，设置设备公开数据
8.  调用sle\_start\_announce，启动设备公开。

**Grant Node：**

1.  调用enable\_sle，打开SLE开关。
2.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
3.  调用sle\_connection\_register\_callbacks，注册连接管理回调函数。
4.  调用sle\_set\_local\_addr，设置本地设备地址。
5.  调用sle\_set\_local\_name，设置本地设备名称。
6.  调用sle\_set\_seek\_param，设置设备发现参数。
7.  调用sle\_start\_seek，启动设备发现，并在回调函数中获得正在进行设备公开的设备信息。
8.  调用sle\_connect\_remote\_device，向对端设备发起连接请求。
9.  调用sle\_pair\_remote\_device，向对端设备发起配对请求。
10. 调用sle\_get\_paired\_devices\_num，获取当前配对设备数量。
11. 调用sle\_get\_paired\_devices，获取当前配对设备信息。
12. 调用sle\_get\_pair\_state，获取配对状态。
13. 调用sle\_get\_connect\_role，获取链路角色。

# SSAP Server接口<a name="ZH-CN_TOPIC_0000001665143030"></a>



## 概述<a name="ZH-CN_TOPIC_0000001665143022"></a>

SSAP是SLE发送和接收数据的通用规范，支持在两个SLE设备间进行数据传输。

## 开发流程<a name="ZH-CN_TOPIC_0000001713102817"></a>

**使用场景<a name="section775mcpsimp"></a>**

SSAP Server主要接收对端的请求和命令，向对端发送响应、通知和指示。

**功能<a name="section779mcpsimp"></a>**

SSAP Server提供的接口如[表1](#_table213321716161)所示。

**表 1**  SSAP Server接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="15.981598159815983%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="33.093309330933096%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.562756275627564%" id="mcps1.*******"><p id="p134231719219"><a name="p134231719219"></a><a name="p134231719219"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p109931320124"><a name="p109931320124"></a><a name="p109931320124"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p16748721184514"><a name="p16748721184514"></a><a name="p16748721184514"></a>ssaps_register_server</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p798mcpsimp"><a name="p798mcpsimp"></a><a name="p798mcpsimp"></a>注册SSAP server。</p>
<p id="p8737184118442"><a name="p8737184118442"></a><a name="p8737184118442"></a><strong id="b938141312474"><a name="b938141312474"></a><a name="b938141312474"></a>注：目前只支持注册一个SSAP  server。</strong></p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p11342191712210"><a name="p11342191712210"></a><a name="p11342191712210"></a>app_uuid：应用UUID指针；</p>
<p id="p382884718520"><a name="p382884718520"></a><a name="p382884718520"></a>server_id：[out] server id指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1399310205210"><a name="p1399310205210"></a><a name="p1399310205210"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p98811272452"><a name="p98811272452"></a><a name="p98811272452"></a>ssaps_unregister_server</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p62365161575"><a name="p62365161575"></a><a name="p62365161575"></a>注销SSAP server。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p23428178216"><a name="p23428178216"></a><a name="p23428178216"></a>server_id：server id。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p1133903217458"><a name="p1133903217458"></a><a name="p1133903217458"></a>ssaps_add_service</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>添加服务。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p2488632383"><a name="p2488632383"></a><a name="p2488632383"></a>server_id：server id；</p>
<p id="p39401883412"><a name="p39401883412"></a><a name="p39401883412"></a>service_uuid：服务UUID；</p>
<p id="p14894321982"><a name="p14894321982"></a><a name="p14894321982"></a>is_primary：是否是首要服务。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p580165015586"><a name="p580165015586"></a><a name="p580165015586"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row14457165322015"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p918263734513"><a name="p918263734513"></a><a name="p918263734513"></a>ssaps_add_property</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p5457753102011"><a name="p5457753102011"></a><a name="p5457753102011"></a>添加特征。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p582817101011"><a name="p582817101011"></a><a name="p582817101011"></a>server_id：server id；</p>
<p id="p160105318410"><a name="p160105318410"></a><a name="p160105318410"></a>service_handle：服务句柄；</p>
<p id="p1682817711010"><a name="p1682817711010"></a><a name="p1682817711010"></a>property：特征信息。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1321275215811"><a name="p1321275215811"></a><a name="p1321275215811"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p1223445218456"><a name="p1223445218456"></a><a name="p1223445218456"></a>ssaps_add_descriptor</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p828mcpsimp"><a name="p828mcpsimp"></a><a name="p828mcpsimp"></a>添加特征描述符。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p620975619104"><a name="p620975619104"></a><a name="p620975619104"></a>server_id：server id；</p>
<p id="p7491721650"><a name="p7491721650"></a><a name="p7491721650"></a>service_handle：服务句柄；</p>
<p id="p8308219135813"><a name="p8308219135813"></a><a name="p8308219135813"></a>property_handle：特征句柄；</p>
<p id="p62091156181015"><a name="p62091156181015"></a><a name="p62091156181015"></a>descriptor：描述符信息。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1699320201023"><a name="p1699320201023"></a><a name="p1699320201023"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1257712474513"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p195621577458"><a name="p195621577458"></a><a name="p195621577458"></a>ssaps_add_service_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p13185839205810"><a name="p13185839205810"></a><a name="p13185839205810"></a>添加服务同步接口，服务句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p44271153867"><a name="p44271153867"></a><a name="p44271153867"></a>server_id：server id；</p>
<p id="p1142718531360"><a name="p1142718531360"></a><a name="p1142718531360"></a>service_uuid：服务UUID；</p>
<p id="p74271953068"><a name="p74271953068"></a><a name="p74271953068"></a>is_primary：是否是首要服务；</p>
<p id="p2447157277"><a name="p2447157277"></a><a name="p2447157277"></a>handle：[out]服务句柄指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p15960115591011"><a name="p15960115591011"></a><a name="p15960115591011"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row386531016"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p177681653469"><a name="p177681653469"></a><a name="p177681653469"></a>ssaps_add_property_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p5732114055213"><a name="p5732114055213"></a><a name="p5732114055213"></a>添加特征同步接口，特征句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p478662517101"><a name="p478662517101"></a><a name="p478662517101"></a>server_id：server id；</p>
<p id="p115851813141312"><a name="p115851813141312"></a><a name="p115851813141312"></a>service_handle：服务句柄；</p>
<p id="p049137111316"><a name="p049137111316"></a><a name="p049137111316"></a>property：特征；</p>
<p id="p6786132541017"><a name="p6786132541017"></a><a name="p6786132541017"></a>handle：[out]特征句柄指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1315723515115"><a name="p1315723515115"></a><a name="p1315723515115"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1913210313121"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p4439121414464"><a name="p4439121414464"></a><a name="p4439121414464"></a>ssaps_add_descriptor_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1180311441527"><a name="p1180311441527"></a><a name="p1180311441527"></a>添加特征描述符同步接口，特征描述符句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p153901413141411"><a name="p153901413141411"></a><a name="p153901413141411"></a>server_id：server id；</p>
<p id="p3390151318144"><a name="p3390151318144"></a><a name="p3390151318144"></a>service_handle：服务句柄；</p>
<p id="p84541922111"><a name="p84541922111"></a><a name="p84541922111"></a>property_handle：特征句柄；</p>
<p id="p1739091315143"><a name="p1739091315143"></a><a name="p1739091315143"></a>descriptor：特征描述符；</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1570763941415"><a name="p1570763941415"></a><a name="p1570763941415"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row829mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p19685122012463"><a name="p19685122012463"></a><a name="p19685122012463"></a>ssaps_start_service</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p833mcpsimp"><a name="p833mcpsimp"></a><a name="p833mcpsimp"></a>启动服务。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1332153919115"><a name="p1332153919115"></a><a name="p1332153919115"></a>server_id：server id；</p>
<p id="p123610581144"><a name="p123610581144"></a><a name="p123610581144"></a>service_handle：服务句柄。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p7174165817582"><a name="p7174165817582"></a><a name="p7174165817582"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row834mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p4504152716467"><a name="p4504152716467"></a><a name="p4504152716467"></a>ssaps_delete_all_services</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p838mcpsimp"><a name="p838mcpsimp"></a><a name="p838mcpsimp"></a>删除所有服务。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p19815155813110"><a name="p19815155813110"></a><a name="p19815155813110"></a>server_id：server id。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p63401159185813"><a name="p63401159185813"></a><a name="p63401159185813"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row844mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p12773143294614"><a name="p12773143294614"></a><a name="p12773143294614"></a>ssaps_send_response</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1230216577176"><a name="p1230216577176"></a><a name="p1230216577176"></a>发送响应。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p16066581212"><a name="p16066581212"></a><a name="p16066581212"></a>server_id：server id；</p>
<p id="p14238121051820"><a name="p14238121051820"></a><a name="p14238121051820"></a>conn_id：连接ID；</p>
<p id="p184988291185"><a name="p184988291185"></a><a name="p184988291185"></a>param：响应参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p17545120155913"><a name="p17545120155913"></a><a name="p17545120155913"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row854mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p1888839194618"><a name="p1888839194618"></a><a name="p1888839194618"></a>ssaps_notify_indicate</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p858mcpsimp"><a name="p858mcpsimp"></a><a name="p858mcpsimp"></a>给对端发送通知或指示。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p189329328226"><a name="p189329328226"></a><a name="p189329328226"></a>server_id：server id；</p>
<p id="p1193223212210"><a name="p1193223212210"></a><a name="p1193223212210"></a>conn_id：连接ID；</p>
<p id="p129321332182214"><a name="p129321332182214"></a><a name="p129321332182214"></a>param：通知或指示参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p8852161145910"><a name="p8852161145910"></a><a name="p8852161145910"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row93013312138"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p96911945164615"><a name="p96911945164615"></a><a name="p96911945164615"></a>ssaps_notify_indicate_by_uuid</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p13301731101316"><a name="p13301731101316"></a><a name="p13301731101316"></a>按照uuid给对端发送通知或指示。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p447113013234"><a name="p447113013234"></a><a name="p447113013234"></a>server_id：server id；</p>
<p id="p7471100162316"><a name="p7471100162316"></a><a name="p7471100162316"></a>conn_id：连接ID；</p>
<p id="p1147116010237"><a name="p1147116010237"></a><a name="p1147116010237"></a>param：通知或指示参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p665017314593"><a name="p665017314593"></a><a name="p665017314593"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row15903162713231"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p360375013461"><a name="p360375013461"></a><a name="p360375013461"></a>ssaps_set_info</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1169284912253"><a name="p1169284912253"></a><a name="p1169284912253"></a>在连接之前设置 server信息。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p16129194355418"><a name="p16129194355418"></a><a name="p16129194355418"></a>server_id：server id；</p>
<p id="p833414605520"><a name="p833414605520"></a><a name="p833414605520"></a>info： server信息。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p2904527172315"><a name="p2904527172315"></a><a name="p2904527172315"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row859mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p12348175544610"><a name="p12348175544610"></a><a name="p12348175544610"></a>ssaps_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p863mcpsimp"><a name="p863mcpsimp"></a><a name="p863mcpsimp"></a>注册SSAP server回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p434291711216"><a name="p434291711216"></a><a name="p434291711216"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p206731868593"><a name="p206731868593"></a><a name="p206731868593"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

SSAP server开发的典型流程：注册SSAP server，注册本端属性数据库，接收对端的请求和命令，向对端发送通知和指示，具体编程实例可参考application/samples/bt。

1.  调用enable\_sle，打开SLE开关。
2.  调用ssaps\_register\_callbacks，注册SSAP server回调。
3.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
4.  调用ssaps\_register\_server，创建一个server实体。
5.  调用ssaps\_add\_service\_sync、ssaps\_add\_property\_sync、ssaps\_add\_descriptor\_sync和ssaps\_start\_service注册本端属性数据库，每一个服务及其内容添加完成后调用ssaps\_start\_service启动服务。
6.  调用sle\_set\_local\_addr，设置本地设备地址。
7.  调用sle\_set\_local\_name，设置本地设备名称。
8.  调用sle\_set\_announce\_param，设置设备公开参数。
9.  调用sle\_set\_announce\_data，设置设备公开数据。
10. 调用sle\_start\_announce，启动设备公开。
11. 连接建立。
12. 接收对端设备的读写请求，当对端设备读写需要授权的特征或描述符时，调用ssaps\_send\_response向对端发送响应并修改本端特征值。
13. 当某个特征的客户端特征配置描述符为0x0001时，在特征值变化时调用ssaps\_notify\_indicate向对端设备发送通知，当某个特征的客户端特征配置描述符为0x0002时，在特征值变化时调用ssaps\_notify\_indicate向对端设备发送指示。

# SSAP client接口<a name="ZH-CN_TOPIC_0000001664983286"></a>



## 概述<a name="ZH-CN_TOPIC_0000001713102821"></a>

SSAP是SLE发送和接收数据的通用规范，支持在两个SLE设备间进行数据传输。

## 开发流程<a name="ZH-CN_TOPIC_0000001665143018"></a>

**使用场景<a name="section775mcpsimp"></a>**

SSAP Client主要向对端发送请求和命令，接收对端的响应、通知和指示。

**功能<a name="section779mcpsimp"></a>**

SSAP Client提供的接口如[表1](#_table213321716161)所示。

**表 1**  SSAP Client接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="17.911791179117913%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="31.16311631163116%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.562756275627564%" id="mcps1.*******"><p id="p134231719219"><a name="p134231719219"></a><a name="p134231719219"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p109931320124"><a name="p109931320124"></a><a name="p109931320124"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p173375295437"><a name="p173375295437"></a><a name="p173375295437"></a>ssapc_register_client</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p798mcpsimp"><a name="p798mcpsimp"></a><a name="p798mcpsimp"></a>注册SSAP client。</p>
<p id="p8737184118442"><a name="p8737184118442"></a><a name="p8737184118442"></a><strong id="b938141312474"><a name="b938141312474"></a><a name="b938141312474"></a>注：目前只支持注册一个SSAP  client。</strong></p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p11342191712210"><a name="p11342191712210"></a><a name="p11342191712210"></a>app_uuid：应用UUID指针；</p>
<p id="p382884718520"><a name="p382884718520"></a><a name="p382884718520"></a>client_id：[out] client id指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1399310205210"><a name="p1399310205210"></a><a name="p1399310205210"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p641943414312"><a name="p641943414312"></a><a name="p641943414312"></a>ssapc_unregister_client</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p62365161575"><a name="p62365161575"></a><a name="p62365161575"></a>注销SSAP client。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p23428178216"><a name="p23428178216"></a><a name="p23428178216"></a>client_id：client id。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p1477684118431"><a name="p1477684118431"></a><a name="p1477684118431"></a>ssapc_find_structure</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>查找对端服务、特征和描述符。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p118617984917"><a name="p118617984917"></a><a name="p118617984917"></a>client_id：client id；</p>
<p id="p1376352075011"><a name="p1376352075011"></a><a name="p1376352075011"></a>conn_id：连接ID；</p>
<p id="p6299724205014"><a name="p6299724205014"></a><a name="p6299724205014"></a>param：查找参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p580165015586"><a name="p580165015586"></a><a name="p580165015586"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row14457165322015"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p537974794310"><a name="p537974794310"></a><a name="p537974794310"></a>ssapc_read_req_by_uuid</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p5457753102011"><a name="p5457753102011"></a><a name="p5457753102011"></a>向对端发送按照uuid读取请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1272115416524"><a name="p1272115416524"></a><a name="p1272115416524"></a>client_id：client id；</p>
<p id="p572954105213"><a name="p572954105213"></a><a name="p572954105213"></a>conn_id：连接ID；</p>
<p id="p1960195515522"><a name="p1960195515522"></a><a name="p1960195515522"></a>param：读取参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1321275215811"><a name="p1321275215811"></a><a name="p1321275215811"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p7400352144312"><a name="p7400352144312"></a><a name="p7400352144312"></a>ssapc_read_req</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p828mcpsimp"><a name="p828mcpsimp"></a><a name="p828mcpsimp"></a>向对端发送读取请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p17114111019536"><a name="p17114111019536"></a><a name="p17114111019536"></a>client_id：client id；</p>
<p id="p15114161015314"><a name="p15114161015314"></a><a name="p15114161015314"></a>conn_id：连接ID；</p>
<p id="p611451017535"><a name="p611451017535"></a><a name="p611451017535"></a>handle：句柄；</p>
<p id="p146691915105311"><a name="p146691915105311"></a><a name="p146691915105311"></a>type：类型。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1699320201023"><a name="p1699320201023"></a><a name="p1699320201023"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1257712474513"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p20433658194314"><a name="p20433658194314"></a><a name="p20433658194314"></a>ssapc_write_req</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p13185839205810"><a name="p13185839205810"></a><a name="p13185839205810"></a>向对端发送写请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p143144511538"><a name="p143144511538"></a><a name="p143144511538"></a>client_id：client id；</p>
<p id="p73184515316"><a name="p73184515316"></a><a name="p73184515316"></a>conn_id：连接ID；</p>
<p id="p143204513532"><a name="p143204513532"></a><a name="p143204513532"></a>param：写参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p15960115591011"><a name="p15960115591011"></a><a name="p15960115591011"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row386531016"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p1145512484411"><a name="p1145512484411"></a><a name="p1145512484411"></a>ssapc_write_cmd</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p5732114055213"><a name="p5732114055213"></a><a name="p5732114055213"></a>向对端发送写命令。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p060712511535"><a name="p060712511535"></a><a name="p060712511535"></a>client_id：client id；</p>
<p id="p116071451135320"><a name="p116071451135320"></a><a name="p116071451135320"></a>conn_id：连接ID；</p>
<p id="p136071651205317"><a name="p136071651205317"></a><a name="p136071651205317"></a>param：写参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1315723515115"><a name="p1315723515115"></a><a name="p1315723515115"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1913210313121"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p830131014411"><a name="p830131014411"></a><a name="p830131014411"></a>ssapc_exchange_info_req</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p1180311441527"><a name="p1180311441527"></a><a name="p1180311441527"></a>向对端发送交换信息请求。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p15525866545"><a name="p15525866545"></a><a name="p15525866545"></a>client_id：client id；</p>
<p id="p65258695412"><a name="p65258695412"></a><a name="p65258695412"></a>conn_id：连接ID；</p>
<p id="p1552512615411"><a name="p1552512615411"></a><a name="p1552512615411"></a>param：交换信息参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1570763941415"><a name="p1570763941415"></a><a name="p1570763941415"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row829mcpsimp"><td class="cellrowborder" valign="top" width="17.911791179117913%" headers="mcps1.******* "><p id="p18516111664413"><a name="p18516111664413"></a><a name="p18516111664413"></a>ssapc_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="31.16311631163116%" headers="mcps1.******* "><p id="p833mcpsimp"><a name="p833mcpsimp"></a><a name="p833mcpsimp"></a>注册SSAP client回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p018012914490"><a name="p018012914490"></a><a name="p018012914490"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p7174165817582"><a name="p7174165817582"></a><a name="p7174165817582"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

SSAP Client开发的典型流程：注册SSAP Client，查找对端属性数据库，向对端发送请求和命令，接收对端的通知和指示，具体编程实例可参考application/samples/bt。

**SSAP Server：**

1.  调用enable\_sle，打开SLE开关。
2.  调用ssaps\_register\_callbacks，注册SSAP server回调。
3.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
4.  调用ssaps\_register\_server，创建一个server实体。
5.  调用ssaps\_add\_service\_sync、ssaps\_add\_property\_sync、ssaps\_add\_descriptor\_sync和ssaps\_start\_service注册本端属性数据库，每一个服务及其内容添加完成后调用ssaps\_start\_service启动服务。
6.  调用sle\_set\_local\_addr，设置本地设备地址。
7.  调用sle\_set\_local\_name，设置本地设备名称。
8.  调用sle\_set\_announce\_param，设置设备公开参数。
9.  调用sle\_set\_announce\_data，设置设备公开数据。
10. 调用sle\_start\_announce，启动设备公开。
11. 连接建立。
12. 接收对端设备的读写请求，当对端设备读写需要授权的特征或描述符时，调用ssaps\_send\_response向对端发送响应并修改本端特征值。
13. 当某个特征的客户端特征配置描述符为0x0001时，在特征值变化时向对端设备发送通知，当某个特征的客户端特征配置描述符为0x0002时，在特征值变化时向对端设备发送指示。

**SSAP Client：**

1.  调用enable\_sle，打开SLE开关。
2.  调用ssapc\_register\_callbacks，注册SSAP client回调。
3.  调用sle\_announce\_seek\_register\_callbacks，注册设备公开和设备发现回调函数。
4.  调用ssapc\_register\_client，创建一个client实体。
5.  递归调用ssapc\_find\_structure查找对端属性数据库。
6.  如果关注对端某个特征，可调用ssapc\_write\_req或ssapc\_write\_cmd将该特征的客户端特征配置描述符写为0x0001或0x0002，前者可使能对端特征通知，后者可使能对端特征指示。
7.  调用读接口ssapc\_read\_req和写接口ssapc\_write\_req操作对端属性数据库。

# Low Latency接口<a name="ZH-CN_TOPIC_0000001713022849"></a>



## 概述<a name="ZH-CN_TOPIC_0000001713022845"></a>

Low Latency模块使用星闪协议以极低时延在服务端和客户端之间进行数据传输。

## 开发流程<a name="ZH-CN_TOPIC_0000001664983282"></a>

**使用场景<a name="section775mcpsimp"></a>**

Low Latency模块的功能是开关服务端和客户端的低时延通道，在服务端发送传感器内数据，在客户端侧接收客户端数据。

**功能<a name="section779mcpsimp"></a>**

Low Latency提供的接口如[表1](#_table213321716161)所示。

**表 1**  Low Latency接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="25.232523252325233%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="23.84238423842384%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.562756275627564%" id="mcps1.*******"><p id="p134231719219"><a name="p134231719219"></a><a name="p134231719219"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p109931320124"><a name="p109931320124"></a><a name="p109931320124"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="25.232523252325233%" headers="mcps1.******* "><p id="p1879411556294"><a name="p1879411556294"></a><a name="p1879411556294"></a>sle_low_latency_mouse_enable</p>
</td>
<td class="cellrowborder" valign="top" width="23.84238423842384%" headers="mcps1.******* "><p id="p193806416295"><a name="p193806416295"></a><a name="p193806416295"></a>打开低时延鼠标。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p19898114512913"><a name="p19898114512913"></a><a name="p19898114512913"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1399310205210"><a name="p1399310205210"></a><a name="p1399310205210"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row290325123919"><td class="cellrowborder" valign="top" width="25.232523252325233%" headers="mcps1.******* "><p id="p3231296303"><a name="p3231296303"></a><a name="p3231296303"></a>sle_low_latency_dongle_enable</p>
</td>
<td class="cellrowborder" valign="top" width="23.84238423842384%" headers="mcps1.******* "><p id="p6380184122911"><a name="p6380184122911"></a><a name="p6380184122911"></a>打开低时延dongle。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p289724552915"><a name="p289724552915"></a><a name="p289724552915"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p580165015586"><a name="p580165015586"></a><a name="p580165015586"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="25.232523252325233%" headers="mcps1.******* "><p id="p161925175119"><a name="p161925175119"></a><a name="p161925175119"></a>sle_low_latency_mouse_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="23.84238423842384%" headers="mcps1.******* "><p id="p1380134102915"><a name="p1380134102915"></a><a name="p1380134102915"></a>注册低时延鼠标回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1089724512911"><a name="p1089724512911"></a><a name="p1089724512911"></a>mouse_cbk：鼠标数据回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row283328115910"><td class="cellrowborder" valign="top" width="25.232523252325233%" headers="mcps1.******* "><p id="p3994171323011"><a name="p3994171323011"></a><a name="p3994171323011"></a>sle_low_latency_set</p>
</td>
<td class="cellrowborder" valign="top" width="23.84238423842384%" headers="mcps1.******* "><p id="p1537944192912"><a name="p1537944192912"></a><a name="p1537944192912"></a>设置低时延速率，打开或关闭低时延。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1889610455295"><a name="p1889610455295"></a><a name="p1889610455295"></a>conn_id：连接ID；</p>
<p id="p4641818335"><a name="p4641818335"></a><a name="p4641818335"></a>enable：是否打开低时延；</p>
<p id="p10706212183313"><a name="p10706212183313"></a><a name="p10706212183313"></a>rate：速率。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1321275215811"><a name="p1321275215811"></a><a name="p1321275215811"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="25.232523252325233%" headers="mcps1.******* "><p id="p685261833011"><a name="p685261833011"></a><a name="p685261833011"></a>sle_low_latency_dongle_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="23.84238423842384%" headers="mcps1.******* "><p id="p237910419290"><a name="p237910419290"></a><a name="p237910419290"></a>注册低时延dongle回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p78952452297"><a name="p78952452297"></a><a name="p78952452297"></a>dongle_cbk：dongle数据回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1699320201023"><a name="p1699320201023"></a><a name="p1699320201023"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

Low Latency开发的典型流程如下：

**Dongle：**

1.  调用sle\_low\_latency\_dongle\_register\_callbacks，注册dongle数据回调函数。
2.  调用sle\_low\_latency\_dongle\_enable接口，打开dongle低时延通道。
3.  调用sle\_low\_latency\_set设置低时延速率。
4.  接收鼠标数据。

**鼠标：**

1.  调用sle\_low\_latency\_mouse\_register\_callbacks，注册鼠标数据回调函数。
2.  调用sle\_low\_latency\_mouse\_enable接口，打开鼠标低时延通道。
3.  移动鼠标。

# Transmission Manager接口<a name="ZH-CN_TOPIC_0000001910818801"></a>



## 概述<a name="ZH-CN_TOPIC_0000001864699584"></a>

Transmission Manager模块负责SLE的数据发送，保障了数据成功传输到底层。

## 开发流程<a name="ZH-CN_TOPIC_0000001910819073"></a>

**使用场景<a name="section4346153310231"></a>**

Transmission Manager模块实时监控了底层缓冲区数量，数据包因为底层发送繁忙没有发出去时，就会上报发送繁忙通知。

**功能<a name="section645413478238"></a>**

Transmission Manager提供的接口如[表1](#table63362010299)所示。

**表 1**  Transmission Manager接口描述

<a name="table63362010299"></a>
<table><thead align="left"><tr id="row113402013297"><th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p23482010297"><a name="p23482010297"></a><a name="p23482010297"></a>接口描述</p>
</th>
<th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p1234182052917"><a name="p1234182052917"></a><a name="p1234182052917"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p173402012298"><a name="p173402012298"></a><a name="p173402012298"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p1934620202914"><a name="p1934620202914"></a><a name="p1934620202914"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row7345201299"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p12547165143717"><a name="p12547165143717"></a><a name="p12547165143717"></a>send_data_cb</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p634120192917"><a name="p634120192917"></a><a name="p634120192917"></a>SLE发送数据繁忙回调勾子。</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p13414201295"><a name="p13414201295"></a><a name="p13414201295"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p13341020152917"><a name="p13341020152917"></a><a name="p13341020152917"></a>-</p>
</td>
</tr>
<tr id="row81161808346"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p1118411710342"><a name="p1118411710342"></a><a name="p1118411710342"></a>sle_transmission_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p101163019348"><a name="p101163019348"></a><a name="p101163019348"></a>注册SLE传输管理回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p13116160143415"><a name="p13116160143415"></a><a name="p13116160143415"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p1011619019349"><a name="p1011619019349"></a><a name="p1011619019349"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section19178325112415"></a>**

Transmission Manager典型开发流程如下：

**SERVER端：**

1.  调用sle\_transmission\_register\_callbacks，注册SLE传输管理回调函数。
2.  每次发包前，根据回调勾子反馈的系统忙闲状态，若是空闲状态，继续高速发包；若是流控状态，减小发包速率；若是繁忙状态，停止发包。

# SLE Passkey接口<a name="ZH-CN_TOPIC_0000002187388469"></a>



## 概述<a name="ZH-CN_TOPIC_0000002151911264"></a>

星闪配对有多种鉴权方式可以选择，passkey鉴权是其中之一，passkey鉴权提供了防中间人攻击能力，安全性相对免输入鉴权有提升。

## 开发流程<a name="ZH-CN_TOPIC_0000002187348429"></a>

**使用场景<a name="section4346153310231"></a>**

发起星闪配对后，根据用户设置的IO（Input Output）能力决策的鉴权方式决策鉴权方式，当满足条件时，协议栈能够自动协商为passkey鉴权。

**功能<a name="section645413478238"></a>**

SLE提供的passkey相关接口如SLE Passkey接口描述如[表1](#table63362010299)所示。

**表 1**  SLE Passkey接口描述

<a name="table63362010299"></a>
<table><thead align="left"><tr id="row113402013297"><th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p23482010297"><a name="p23482010297"></a><a name="p23482010297"></a>接口描述</p>
</th>
<th class="cellrowborder" valign="top" width="17.23%" id="mcps1.*******"><p id="p1234182052917"><a name="p1234182052917"></a><a name="p1234182052917"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="29.189999999999998%" id="mcps1.*******"><p id="p173402012298"><a name="p173402012298"></a><a name="p173402012298"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="28.58%" id="mcps1.*******"><p id="p1934620202914"><a name="p1934620202914"></a><a name="p1934620202914"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row7345201299"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p4960181710107"><a name="p4960181710107"></a><a name="p4960181710107"></a>passkey_req_cb</p>
</td>
<td class="cellrowborder" valign="top" width="17.23%" headers="mcps1.******* "><p id="p634120192917"><a name="p634120192917"></a><a name="p634120192917"></a>SLE配对发起后，passkey请求回调勾子。</p>
</td>
<td class="cellrowborder" valign="top" width="29.189999999999998%" headers="mcps1.******* "><p id="p333614563105"><a name="p333614563105"></a><a name="p333614563105"></a>conn_id：SLE连接ID。</p>
</td>
<td class="cellrowborder" valign="top" width="28.58%" headers="mcps1.******* "><p id="p13341020152917"><a name="p13341020152917"></a><a name="p13341020152917"></a>-</p>
</td>
</tr>
<tr id="row1026520252104"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p841442716104"><a name="p841442716104"></a><a name="p841442716104"></a>passkey_notify_cb</p>
</td>
<td class="cellrowborder" valign="top" width="17.23%" headers="mcps1.******* "><p id="p1265122514102"><a name="p1265122514102"></a><a name="p1265122514102"></a>SLE配对发起后，passkey确认回调勾子。</p>
</td>
<td class="cellrowborder" valign="top" width="29.189999999999998%" headers="mcps1.******* "><p id="p19265122591015"><a name="p19265122591015"></a><a name="p19265122591015"></a>conn_id：SLE连接ID；</p>
<p id="p493611101212"><a name="p493611101212"></a><a name="p493611101212"></a>passkey：通行码数据指针；</p>
<p id="p370132419124"><a name="p370132419124"></a><a name="p370132419124"></a>len：通行码数据长度。</p>
</td>
<td class="cellrowborder" valign="top" width="28.58%" headers="mcps1.******* "><p id="p42650253106"><a name="p42650253106"></a><a name="p42650253106"></a>-</p>
</td>
</tr>
<tr id="row1825034913128"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p87519529128"><a name="p87519529128"></a><a name="p87519529128"></a>sle_passkey_entry</p>
</td>
<td class="cellrowborder" valign="top" width="17.23%" headers="mcps1.******* "><p id="p12501749141219"><a name="p12501749141219"></a><a name="p12501749141219"></a>通行码输入。</p>
</td>
<td class="cellrowborder" valign="top" width="29.189999999999998%" headers="mcps1.******* "><p id="p225074915127"><a name="p225074915127"></a><a name="p225074915127"></a>conn_id：SLE连接ID；</p>
<p id="p6244214171319"><a name="p6244214171319"></a><a name="p6244214171319"></a>passkey：转为数字的通信码数据。</p>
</td>
<td class="cellrowborder" valign="top" width="28.58%" headers="mcps1.******* "><p id="p172501649191210"><a name="p172501649191210"></a><a name="p172501649191210"></a>成功：ERRCODE_SLE_SUCCESS；</p>
<p id="p10194191818145"><a name="p10194191818145"></a><a name="p10194191818145"></a>失败：对应错误码。</p>
</td>
</tr>
</tbody>
</table>

SLE提供的安全参数设置接口如SLE安全参数设置接口描述如[表2](#table496914359166)所示。

**表 2**  SLE安全参数设置接口描述

<a name="table496914359166"></a>
<table><thead align="left"><tr id="row896918355165"><th class="cellrowborder" valign="top" width="25%" id="mcps1.*******"><p id="p1296915350163"><a name="p1296915350163"></a><a name="p1296915350163"></a>接口描述</p>
</th>
<th class="cellrowborder" valign="top" width="17.23%" id="mcps1.*******"><p id="p19969143591614"><a name="p19969143591614"></a><a name="p19969143591614"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="29.189999999999998%" id="mcps1.*******"><p id="p5969103518166"><a name="p5969103518166"></a><a name="p5969103518166"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="28.58%" id="mcps1.*******"><p id="p1696917352166"><a name="p1696917352166"></a><a name="p1696917352166"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row149691235181615"><td class="cellrowborder" valign="top" width="25%" headers="mcps1.******* "><p id="p164652126172"><a name="p164652126172"></a><a name="p164652126172"></a>sle_set_sec_param</p>
</td>
<td class="cellrowborder" valign="top" width="17.23%" headers="mcps1.******* "><p id="p1096910356166"><a name="p1096910356166"></a><a name="p1096910356166"></a>SLE设置安全参数。</p>
</td>
<td class="cellrowborder" valign="top" width="29.189999999999998%" headers="mcps1.******* "><p id="p196914353160"><a name="p196914353160"></a><a name="p196914353160"></a>sec_params：SLE安全参数，包含IO能力、配对绑定地址、配对防中间人攻击、地址隐私使能。</p>
</td>
<td class="cellrowborder" valign="top" width="28.58%" headers="mcps1.******* "><p id="p1735562131910"><a name="p1735562131910"></a><a name="p1735562131910"></a>成功：ERRCODE_SLE_SUCCESS；</p>
<p id="p143555219195"><a name="p143555219195"></a><a name="p143555219195"></a>失败：对应错误码。</p>
</td>
</tr>
</tbody>
</table>

SLE协议提供的IO能力和鉴权方式对应关系如SLE鉴权方式决策描述如[表3](#table025793011253)所示。

**表 3**  SLE鉴权方式决策描述

<a name="table025793011253"></a>
<table><tbody><tr id="row22571730162515"><td class="cellrowborder" colspan="2" valign="top">&nbsp;&nbsp;</td>
<td class="cellrowborder" colspan="5" valign="top"><p id="p64711846152518"><a name="p64711846152518"></a><a name="p64711846152518"></a>G节点IO能力</p>
</td>
</tr>
<tr id="row825793072517"><td class="cellrowborder" colspan="2" valign="top">&nbsp;&nbsp;</td>
<td class="cellrowborder" valign="top"><p id="p107114119295"><a name="p107114119295"></a><a name="p107114119295"></a>只展示</p>
</td>
<td class="cellrowborder" valign="top"><p id="p2334102015299"><a name="p2334102015299"></a><a name="p2334102015299"></a>展示，并且可以选择Yes或者No</p>
</td>
<td class="cellrowborder" valign="top"><p id="p157625475292"><a name="p157625475292"></a><a name="p157625475292"></a>只支持键盘</p>
</td>
<td class="cellrowborder" valign="top"><p id="p52571558172918"><a name="p52571558172918"></a><a name="p52571558172918"></a>没有输入输出</p>
</td>
<td class="cellrowborder" valign="top"><p id="p239915833015"><a name="p239915833015"></a><a name="p239915833015"></a>支持键盘和展示</p>
</td>
</tr>
<tr id="row3257330192512"><td class="cellrowborder" rowspan="5" valign="top" width="7.787663700889732%"><p id="p1759488182610"><a name="p1759488182610"></a><a name="p1759488182610"></a>T节点IO能力</p>
</td>
<td class="cellrowborder" valign="top" width="15.595321403578927%"><p id="p8257153022512"><a name="p8257153022512"></a><a name="p8257153022512"></a>只展示</p>
</td>
<td class="cellrowborder" valign="top" width="13.86584024792562%"><p id="p825773014253"><a name="p825773014253"></a><a name="p825773014253"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top" width="15.545336399080275%"><p id="p10257153012520"><a name="p10257153012520"></a><a name="p10257153012520"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top" width="16.934919524142757%"><p id="p025773010253"><a name="p025773010253"></a><a name="p025773010253"></a>Passkey通行码鉴权</p>
</td>
<td class="cellrowborder" valign="top" width="15.985204438668399%"><p id="p172571330132517"><a name="p172571330132517"></a><a name="p172571330132517"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top" width="14.285714285714285%"><p id="p10257530152516"><a name="p10257530152516"></a><a name="p10257530152516"></a>Passkey通行码鉴权</p>
</td>
</tr>
<tr id="row2025733022516"><td class="cellrowborder" valign="top"><p id="p1925714305258"><a name="p1925714305258"></a><a name="p1925714305258"></a>展示，并且可以选择Yes或者No</p>
</td>
<td class="cellrowborder" valign="top"><p id="p15257103062510"><a name="p15257103062510"></a><a name="p15257103062510"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p1725712308250"><a name="p1725712308250"></a><a name="p1725712308250"></a>-</p>
</td>
<td class="cellrowborder" valign="top"><p id="p7258930192517"><a name="p7258930192517"></a><a name="p7258930192517"></a>Passkey通行码鉴权</p>
</td>
<td class="cellrowborder" valign="top"><p id="p225818306258"><a name="p225818306258"></a><a name="p225818306258"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p025833011255"><a name="p025833011255"></a><a name="p025833011255"></a>-</p>
</td>
</tr>
<tr id="row12258153082519"><td class="cellrowborder" valign="top"><p id="p4286175110291"><a name="p4286175110291"></a><a name="p4286175110291"></a>只支持键盘</p>
</td>
<td class="cellrowborder" valign="top"><p id="p325853012256"><a name="p325853012256"></a><a name="p325853012256"></a>Passkey通行码鉴权</p>
</td>
<td class="cellrowborder" valign="top"><p id="p1258163011258"><a name="p1258163011258"></a><a name="p1258163011258"></a>Passkey通行码鉴权</p>
</td>
<td class="cellrowborder" valign="top"><p id="p12582300254"><a name="p12582300254"></a><a name="p12582300254"></a>-</p>
</td>
<td class="cellrowborder" valign="top"><p id="p18258730192519"><a name="p18258730192519"></a><a name="p18258730192519"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p3258163042510"><a name="p3258163042510"></a><a name="p3258163042510"></a>-</p>
</td>
</tr>
<tr id="row12258193062512"><td class="cellrowborder" valign="top"><p id="p192000153011"><a name="p192000153011"></a><a name="p192000153011"></a>没有输入输出</p>
</td>
<td class="cellrowborder" valign="top"><p id="p1425810302257"><a name="p1425810302257"></a><a name="p1425810302257"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p925812301259"><a name="p925812301259"></a><a name="p925812301259"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p92581430132514"><a name="p92581430132514"></a><a name="p92581430132514"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p18258830172515"><a name="p18258830172515"></a><a name="p18258830172515"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p025814302258"><a name="p025814302258"></a><a name="p025814302258"></a>免输入</p>
</td>
</tr>
<tr id="row192581230102519"><td class="cellrowborder" valign="top"><p id="p15245131117304"><a name="p15245131117304"></a><a name="p15245131117304"></a>支持键盘和展示</p>
</td>
<td class="cellrowborder" valign="top"><p id="p425817304256"><a name="p425817304256"></a><a name="p425817304256"></a>Passkey通行码鉴权</p>
</td>
<td class="cellrowborder" valign="top"><p id="p152581630142515"><a name="p152581630142515"></a><a name="p152581630142515"></a>-</p>
</td>
<td class="cellrowborder" valign="top"><p id="p42589305255"><a name="p42589305255"></a><a name="p42589305255"></a>-</p>
</td>
<td class="cellrowborder" valign="top"><p id="p32581230172511"><a name="p32581230172511"></a><a name="p32581230172511"></a>免输入</p>
</td>
<td class="cellrowborder" valign="top"><p id="p225815305252"><a name="p225815305252"></a><a name="p225815305252"></a>-</p>
</td>
</tr>
</tbody>
</table>

IO能力定义参考枚举：sle\_io\_ability\_t

**开发流程<a name="section161008381712"></a>**

SLE Passkey鉴权典型开发流程如下：

1.  初始化SLE协议栈之后，互联的两个设备必须调用安全参数设置接口，配置双方设备的IO能力、防中间人攻击标识。

    配对决策使用passkey鉴权时，需将防中间人攻击标识置为：SLE\_PAIRING\_DEFEND\_MITM，双方设备的IO能力按照表SLE鉴权方式决策描述进行设置。

2.  连接端发起连接，并确保连接完成。
3.  连接完成后，调用配对发起接口：sle\_pair\_remote\_device，该接口在G、T节点均可调用。
4.  通过安全参数设置接口，配置为显示设备（IO能力为：只展示或展示并可以选择Yes或者No），则该设备只会收到passkey\_notify\_cb回调，此时该设备需要将数据显示，passkey在当前设备上由协议栈生成随机6位数字。
5.  通过安全参数设置接口，配置为输入设备（IO能力为：只支持键盘或支持键盘和展示），则该设备只会收到passkey\_req\_cb回调，此时该设备需要用户输入对端设备显示的passkey。
6.  输入设备调用passkey输入接口成功后，等待配对完成，典型时长2s\~4s。

SLE配对，选择Passkey鉴权异常处理如SLE Passkey异常处理描述，如[表4](#table541115919524)所示。

**表 4**  SLE Passkey异常处理描述

<a name="table541115919524"></a>
<table><thead align="left"><tr id="row16411175918523"><th class="cellrowborder" valign="top" width="16.181618161816182%" id="mcps1.2.4.1.1"><p id="p441145935211"><a name="p441145935211"></a><a name="p441145935211"></a>异常情形</p>
</th>
<th class="cellrowborder" valign="top" width="61.37613761376137%" id="mcps1.2.4.1.2"><p id="p20411125965211"><a name="p20411125965211"></a><a name="p20411125965211"></a>异常处理</p>
</th>
<th class="cellrowborder" valign="top" width="22.442244224422442%" id="mcps1.2.4.1.3"><p id="p5411155965220"><a name="p5411155965220"></a><a name="p5411155965220"></a>异常影响或措施</p>
</th>
</tr>
</thead>
<tbody><tr id="row1411059155214"><td class="cellrowborder" valign="top" width="16.181618161816182%" headers="mcps1.2.4.1.1 "><p id="p124111159185219"><a name="p124111159185219"></a><a name="p124111159185219"></a>passkey输入超时</p>
</td>
<td class="cellrowborder" valign="top" width="61.37613761376137%" headers="mcps1.2.4.1.2 "><p id="p1841135915220"><a name="p1841135915220"></a><a name="p1841135915220"></a>当输入设备端，超过30s没有调用passkey输入接口时，协议栈断开连接。</p>
</td>
<td class="cellrowborder" valign="top" width="22.442244224422442%" headers="mcps1.2.4.1.3 "><p id="p1441195915210"><a name="p1441195915210"></a><a name="p1441195915210"></a>断开后可以重新发起配对</p>
</td>
</tr>
<tr id="row5229151195516"><td class="cellrowborder" valign="top" width="16.181618161816182%" headers="mcps1.2.4.1.1 "><p id="p12291611185512"><a name="p12291611185512"></a><a name="p12291611185512"></a>passkey输入错误</p>
</td>
<td class="cellrowborder" valign="top" width="61.37613761376137%" headers="mcps1.2.4.1.2 "><p id="p822915112559"><a name="p822915112559"></a><a name="p822915112559"></a>当输入设备端，输入的passkey值和显示设备不一致时，协议栈直接断开连接。</p>
</td>
<td class="cellrowborder" valign="top" width="22.442244224422442%" headers="mcps1.2.4.1.3 "><p id="p922931110551"><a name="p922931110551"></a><a name="p922931110551"></a>断开后可以重新发起配对</p>
</td>
</tr>
</tbody>
</table>

SLE配对，选择Passkey鉴权时，显示设备可选设置默认显示的passkey：

1.  初始化SLE协议栈后，调用安全参数设置接口。
2.  连接端发起连接，连接成功。
3.  显示设备（IO能力：只展示或展示并可以选择Yes或者No）在连接成功后，调用sle\_passkey\_entry接口设置显示端默认passkey。
4.  任意设备调用配对发起接口，发起配对。
5.  输入设备（IO能力为：只支持键盘或支持键盘和展示）上收到请求后，输入默认passkey。
6.  等待配对完成。

