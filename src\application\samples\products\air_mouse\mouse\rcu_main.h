/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved. \n
 *
 * Description: Air mouse RCU Main Task Header File. \n
 *
 * History: \n
 * 2024-10-14, Create file. \n
 */

#ifndef RCU_MAIN_H
#define RCU_MAIN_H

#include <stdint.h>

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

void *rcu_task(const char *arg);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif
