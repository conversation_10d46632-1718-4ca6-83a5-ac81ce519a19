set(COMPONENT_NAME "liteos_208_6_0_b017")

get_filename_component(LITEOS_TOP_DIR "${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS" ABSOLUTE)
set(LITEOSTOPDIR ${LITEOS_TOP_DIR})
set(VERSION_NUM "7.3.0" CACHE STRING "GCC VERSION" FORCE)

include(${LITEOS_TOP_DIR}/build/cmake/function.cmake)

if(DEFINED LITEOS_KCONFIG)
    set(LITEOS_SRC_CONFIG ${LITEOSTOPDIR}/tools/build/config/${LITEOS_KCONFIG}.config)
else()
    set(LITEOS_SRC_CONFIG ${LITEOSTOPDIR}/tools/build/config/${CHIP}.config)
endif()

set(LITEOS_DST_CONFIG ${LITEOSTOPDIR}/.config)
# filter-out and setup LOSCFG_PLATFORM only
IMPORT_BOARD_CONFIG(LOSCFG_PLATFORM= ${LITEOS_SRC_CONFIG} IGNORE)
# remove "" maybe in LOSCFG_PLATFORM
string(REGEX REPLACE \" "" LITEOS_PLATFORM "${LOSCFG_PLATFORM}")
string(REGEX REPLACE \" "" LITEOS_PLATFORM "${LOSCFG_PLATFORM}")

############### updating .config/menuconfig.h files ###############
set(LITEOS_MENUCONFIG_H ${LITEOSTOPDIR}/targets/menuconfig.h)
# LITEOS_CHECK_MENUCONFIG_H is generated by python script for compared with file generated by cmake.
set(LITEOS_CHECK_MENUCONFIG_H
    ${CMAKE_CURRENT_BINARY_DIR}/menuconfig/menuconfig.h)
set(LITEOS_PLATFORM_MENUCONFIG_H ${CMAKE_CURRENT_BINARY_DIR}/menuconfig/include/menuconfig.h
    CACHE STRING "LiteOS target menuconfig.h" FORCE)

############### setup cmake env ###############
if(NOT EXISTS ${LITEOS_PLATFORM_MENUCONFIG_H})
    IMPORT_BOARD_CONFIG(LOSCFG_ ${LITEOS_SRC_CONFIG} ${LITEOS_PLATFORM_MENUCONFIG_H})
else()
    IMPORT_BOARD_CONFIG(LOSCFG_ ${LITEOS_SRC_CONFIG} False)
endif()

set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/liteos/libs)
set(LIB_OUT_PATH ${CMAKE_BINARY_DIR}/liteos/libs)

include(${LITEOS_TOP_DIR}/build/cmake/config.cmake)

add_library(${COMPONENT_NAME}_interface INTERFACE)
get_target_property(LOS_HEADER ${LOS_CC_PROP_INTF_PUB} INTERFACE_INCLUDE_DIRECTORIES)
get_target_property(LOS_DEF ${LOS_CC_PROP_INTF_PUB} INTERFACE_COMPILE_DEFINITIONS)
FOREACH(CUR_INTERFACE ${LOS_INTF_DEP_TARGETS})
    get_target_property(HEADER_TMP ${CUR_INTERFACE} INTERFACE_INCLUDE_DIRECTORIES)
    get_target_property(DEF_TMP ${CUR_INTERFACE} INTERFACE_COMPILE_DEFINITIONS)
    list(APPEND LOS_HEADER ${HEADER_TMP})
    list(APPEND LOS_DEF ${DEF_TMP})
ENDFOREACH(CUR_INTERFACE)

list(APPEND LOS_HEADER
${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/compat/linux/include
${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/compat/cmsis/
${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/open_source/CMSIS/CMSIS/RTOS/LiteOS/INC/
${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/open_source/CMSIS/Core/Include
${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/open_source/CMSIS/CMSIS/RTOS2/Include
)

target_include_directories(${COMPONENT_NAME}_interface
    INTERFACE
    ${LOS_HEADER}
)
target_compile_definitions(${COMPONENT_NAME}_interface
    INTERFACE
    ${LOS_DEF}
)
target_compile_options(${COMPONENT_NAME}_interface
    INTERFACE
    -include${LITEOS_PLATFORM_MENUCONFIG_H}
)

install_sdk(${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/compat/linux/include "*")
install_sdk(${CMAKE_CURRENT_SOURCE_DIR}/Huawei_LiteOS/open_source/CMSIS/CMSIS/RTOS2/Include "*")

get_target_property(COM_HEADER ${COMPONENT_NAME}_interface INTERFACE_COMPILE_OPTIONS)
MESSAGE(STATUS "COM_HEADER${COM_HEADER}")

list(APPEND LITEOS_BASELIB "base" "cmsis_kernel" "cmsis_user" "csysdeps" "driverbase" "init" "interrupt" "riscv" "targets")
list(REMOVE_DUPLICATES LITEOS_BASELIB)
set(LOS_LIB "${LITEOS_BASELIB}" CACHE INTERNAL "" FORCE)
list(REMOVE_ITEM LITEOS_BASELIB "m" "c" "gcc")

list(FIND DEFINES "BS25_SUPPORT_CPP" match)
if(NOT match EQUAL -1)
    list(REMOVE_ITEM LITEOS_BASELIB "stdc++" "supc++")
endif()

target_link_libraries(${TARGET_NAME}
PRIVATE
 -Wl,--whole-archive  ${LITEOS_BASELIB} -Wl,--no-whole-archive
)

foreach(LITEOS_LIB ${LITEOS_DEP_LIBS_INT})
    get_target_property(PUBLIC_HEADER ${LITEOS_LIB} PRIVATE_HEADER)
    get_target_property(PRIVATE_HEADER ${LITEOS_LIB}_intf_pri INTERFACE_INCLUDE_DIRECTORIES)
    get_target_property(SOURCES ${LITEOS_LIB} SOURCES)
    get_target_property(SOURCE_DIR ${LITEOS_LIB} SOURCE_DIR)
    list(FIND DEFINES "SLEM_CARKEY" match)
    if(NOT match EQUAL -1)
        if(${LITEOS_LIB} STREQUAL "c")
            list(APPEND SOURCES "${ROOT_DIR}/kernel/liteos/liteos_v208.6.0_b017/Huawei_LiteOS/open_source/musl/src/complex/cabsf.c")
            list(APPEND SOURCES "${ROOT_DIR}/kernel/liteos/liteos_v208.6.0_b017/Huawei_LiteOS/open_source/musl/src/complex/csqrtf.c")
            set_target_properties(${LITEOS_LIB} PROPERTIES SOURCES "${SOURCES}")
            set_target_properties(${LITEOS_LIB} PROPERTIES COMPILE_FLAGS "${COMPILE_FLAGS} -Wno-unused-but-set-variable -Wno-unknown-pragmas")
        endif()
    endif()
    string(REPLACE "$<$<COMPILE_LANGUAGE:C>:" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
    string(REPLACE "$<$<COMPILE_LANGUAGE:CXX>:" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
    string(REPLACE "$<$<COMPILE_LANGUAGE:ASM>:" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
    string(REPLACE ">" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
    set(SOURCES_REL ${SOURCES})
    if(${LITEOS_LIB} STREQUAL "sec")
        install_sdk(${LITEOSTOPDIR}/platform/libsec/src "*.inl")
    endif()
    list(FILTER SOURCES_REL EXCLUDE REGEX "${ROOT_DIR}/*")
    list(FILTER SOURCES INCLUDE REGEX "${ROOT_DIR}/*")
    list(TRANSFORM SOURCES_REL PREPEND "${SOURCE_DIR}/")
    set(SOURCES ${SOURCES} ${SOURCES_REL})
    set(COMPONENT_NAME ${LITEOS_LIB})
    install_file()
endforeach(LITEOS_LIB LITEOS_DEP_LIBS_INT)
UNSET(PRIVATE_HEADER)
UNSET(PUBLIC_HEADER)
UNSET(SOURCES)

string(REPLACE "$<$<COMPILE_LANGUAGE:C>:" "" PRIVATE_HEADER "${LOS_HEADER}")
string(REPLACE "$<$<COMPILE_LANGUAGE:CXX>:" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
string(REPLACE "$<$<COMPILE_LANGUAGE:ASM>:" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
string(REPLACE ">" "" PRIVATE_HEADER "${PRIVATE_HEADER}")
install_file()
UNSET(PRIVATE_HEADER)

install_sdk(${LITEOSTOPDIR}/build/cmake "*")
install_sdk(${LITEOSTOPDIR}/build/menuconfig "*")
install_sdk(${LITEOS_SRC_CONFIG} "*")
install_sdk(${LITEOSTOPDIR}/.config "*")
install_sdk(${LITEOSTOPDIR}/arch/common "*")
install_sdk(${LITEOSTOPDIR}/lib/libm "*")
install_sdk(${LITEOSTOPDIR}/lib/libc "*")
install_sdk(${LITEOSTOPDIR}/targets/ "Kconfig*")
install_sdk(${LITEOSTOPDIR}/kernel/base/mem/common "*")
install_sdk(${CMAKE_CURRENT_SOURCE_DIR}/show_menuconfig.py "*")
install_sdk(${LITEOSTOPDIR}/kernel/extended/cpup "*")
install_sdk(${LITEOSTOPDIR}/kernel/base/debug "*")

if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${TARGET_COMMAND})
    file(GLOB LOS_LIBS RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/${TARGET_COMMAND} "${TARGET_COMMAND}/lib*.a")
    list(TRANSFORM LOS_LIBS REPLACE "lib([0-9a-zA-Z_]*)\.a" "\\1")
    foreach(LITEOS_LIB ${LOS_LIBS})
        add_library(${LITEOS_LIB} SHARED IMPORTED GLOBAL)
        set_property(TARGET ${LITEOS_LIB} PROPERTY IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/${TARGET_COMMAND}/lib${LITEOS_LIB}.a)
        install_sdk(${CMAKE_CURRENT_SOURCE_DIR}/${TARGET_COMMAND}/lib${LITEOS_LIB}.a "*")
    endforeach()
endif()
