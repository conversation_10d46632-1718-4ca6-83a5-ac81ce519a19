# 0: ECDSA_SHA256
# 1: ECDSA_SHA256_LEA_CTR
# 2: ECDSA_SHA256_LEA_CBC_MAC
# 3: ECDSA_SHA256_AES_CBC

# 10: SM2_SM3
# 11: SM2_SM3_LEA_CTR
# 12: SM2_SM3_LEA_CBC_MAC
# 13: SM2_SM3_SM4_CBC

SignSuite=0

SrcFile=../../../../output/bs21e/acore/bs21e-1100e-slp/application.bin
DstFile=../../../../output/bs21e/acore/bs21e-1100e-slp/application_sign.bin

RootKeyFile=
SubKeyFile=

# Default Single sign mode. If there are 'RootKeyFileExt' & 'SubKeyFileExt' then means Double Sign mode.
# RootKeyFileExt=
# SubKeyFileExt=

ImageId=0x4B0F2D1E
CodeInfoImageId=0x4B0F2D2D
KeyOwnerId=1
KeyId=1
KeyAlg=0x2A13C812
KeyVersion=0xd0000000
KeyVersionMask=0x00000000
Msid=0x00000000
MsidMask=0x00000000
CompressFlag=0

Version=0x00000000
VersionMask=0x00000000

ProtectionKeyL1=00112233445566778899AABBCCDDEEFF
ProtectionKeyL2=00112233445566778899AABBCCDDEEFF
PlainKey=04040404040404040404040404040404
#03030303030303030303030303030303
#0F1E2D3C4B5A69788796A5B4C3D2E1F0
PlainKeyAuth=04040404040404040404040404040404
#0F1E2D3C4B5A69788796A5B4C3D2E1F0
Iv=101112131415161718191A1B1C1D1E1F
#01010101010101010101010101010101
#04040404040404040404040404040404
#101112131415161718191A1B1C1D1E1F

# ProtectionKeyL1Auth=
# ProtectionKeyL2Auth=
# PlainKeyAuth=

TextSegmentSize=0x00001000
RamSize=0x00010000
