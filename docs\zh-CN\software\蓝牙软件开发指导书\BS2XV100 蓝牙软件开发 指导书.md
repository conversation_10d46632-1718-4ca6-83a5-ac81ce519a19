# 前言<a name="ZH-CN_TOPIC_0000001713103213"></a>

**概述<a name="section669mcpsimp"></a>**

BS2XV100通过API（Application Programming Interface）向开发者提供接入和使用低功耗蓝牙的相关接口，包括GAP广播、连接以及GATT服务注册、服务发现等，其他协议相关接口将在后续增量发布。

**产品版本<a name="section673mcpsimp"></a>**

与本文档对应的产品版本如下。

<a name="table676mcpsimp"></a>
<table><thead align="left"><tr id="row681mcpsimp"><th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.1"><p id="p683mcpsimp"><a name="p683mcpsimp"></a><a name="p683mcpsimp"></a><strong id="b684mcpsimp"><a name="b684mcpsimp"></a><a name="b684mcpsimp"></a>产品名称</strong></p>
</th>
<th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.2"><p id="p686mcpsimp"><a name="p686mcpsimp"></a><a name="p686mcpsimp"></a><strong id="b687mcpsimp"><a name="b687mcpsimp"></a><a name="b687mcpsimp"></a>产品版本</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row689mcpsimp"><td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.1 "><p id="p691mcpsimp"><a name="p691mcpsimp"></a><a name="p691mcpsimp"></a>BS2X</p>
</td>
<td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.2 "><p id="p693mcpsimp"><a name="p693mcpsimp"></a><a name="p693mcpsimp"></a>V100</p>
</td>
</tr>
</tbody>
</table>

**读者对象<a name="section694mcpsimp"></a>**

本文档主要适用以下工程师：

-   技术支持工程师
-   软件开发工程师

**符号约定<a name="section133020216410"></a>**

在本文中可能出现下列标志，它们所代表的含义如下。

<a name="table2622507016410"></a>
<table><thead align="left"><tr id="row1530720816410"><th class="cellrowborder" valign="top" width="20.580000000000002%" id="mcps1.1.3.1.1"><p id="p6450074116410"><a name="p6450074116410"></a><a name="p6450074116410"></a><strong id="b2136615816410"><a name="b2136615816410"></a><a name="b2136615816410"></a>符号</strong></p>
</th>
<th class="cellrowborder" valign="top" width="79.42%" id="mcps1.1.3.1.2"><p id="p5435366816410"><a name="p5435366816410"></a><a name="p5435366816410"></a><strong id="b5941558116410"><a name="b5941558116410"></a><a name="b5941558116410"></a>说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row1372280416410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p3734547016410"><a name="p3734547016410"></a><a name="p3734547016410"></a><a name="image2670064316410"></a><a name="image2670064316410"></a><span><img class="" id="image2670064316410" src="figures/zh-cn_image_0000001713103225.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p1757432116410"><a name="p1757432116410"></a><a name="p1757432116410"></a>表示如不避免则将会导致死亡或严重伤害的具有高等级风险的危害。</p>
</td>
</tr>
<tr id="row466863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1432579516410"><a name="p1432579516410"></a><a name="p1432579516410"></a><a name="image4895582316410"></a><a name="image4895582316410"></a><span><img class="" id="image4895582316410" src="figures/zh-cn_image_0000001713023237.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p959197916410"><a name="p959197916410"></a><a name="p959197916410"></a>表示如不避免则可能导致死亡或严重伤害的具有中等级风险的危害。</p>
</td>
</tr>
<tr id="row123863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1232579516410"><a name="p1232579516410"></a><a name="p1232579516410"></a><a name="image1235582316410"></a><a name="image1235582316410"></a><span><img class="" id="image1235582316410" src="figures/zh-cn_image_0000001664983698.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p123197916410"><a name="p123197916410"></a><a name="p123197916410"></a>表示如不避免则可能导致轻微或中度伤害的具有低等级风险的危害。</p>
</td>
</tr>
<tr id="row5786682116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p2204984716410"><a name="p2204984716410"></a><a name="p2204984716410"></a><a name="image4504446716410"></a><a name="image4504446716410"></a><span><img class="" id="image4504446716410" src="figures/zh-cn_image_0000001713103221.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4388861916410"><a name="p4388861916410"></a><a name="p4388861916410"></a>用于传递设备或环境安全警示信息。如不避免则可能会导致设备损坏、数据丢失、设备性能降低或其它不可预知的结果。</p>
<p id="p1238861916410"><a name="p1238861916410"></a><a name="p1238861916410"></a>“须知”不涉及人身伤害。</p>
</td>
</tr>
<tr id="row2856923116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p5555360116410"><a name="p5555360116410"></a><a name="p5555360116410"></a><a name="image799324016410"></a><a name="image799324016410"></a><span><img class="" id="image799324016410" src="figures/zh-cn_image_0000001665143406.png" width="47.88" height="15.96"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4612588116410"><a name="p4612588116410"></a><a name="p4612588116410"></a>对正文中重点信息的补充说明。</p>
<p id="p1232588116410"><a name="p1232588116410"></a><a name="p1232588116410"></a>“说明”不是安全警示信息，不涉及人身、设备及环境伤害信息。</p>
</td>
</tr>
</tbody>
</table>

**修改记录<a name="section2467512116410"></a>**

<a name="table1557726816410"></a>
<table><thead align="left"><tr id="row2942532716410"><th class="cellrowborder" valign="top" width="19.009999999999998%" id="mcps1.1.4.1.1"><p id="p3778275416410"><a name="p3778275416410"></a><a name="p3778275416410"></a><strong id="b5687322716410"><a name="b5687322716410"></a><a name="b5687322716410"></a>文档版本</strong></p>
</th>
<th class="cellrowborder" valign="top" width="25.629999999999995%" id="mcps1.1.4.1.2"><p id="p5627845516410"><a name="p5627845516410"></a><a name="p5627845516410"></a><strong id="b5800814916410"><a name="b5800814916410"></a><a name="b5800814916410"></a>发布日期</strong></p>
</th>
<th class="cellrowborder" valign="top" width="55.36%" id="mcps1.1.4.1.3"><p id="p2382284816410"><a name="p2382284816410"></a><a name="p2382284816410"></a><strong id="b3316380216410"><a name="b3316380216410"></a><a name="b3316380216410"></a>修改说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row2619527184611"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p962032794619"><a name="p962032794619"></a><a name="p962032794619"></a>04</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p176201278464"><a name="p176201278464"></a><a name="p176201278464"></a>2025-01-14</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><a name="ul450643984611"></a><a name="ul450643984611"></a><ul id="ul450643984611"><li>更新<span id="ph171301014101610"><a name="ph171301014101610"></a><a name="ph171301014101610"></a>“<a href="GAP接口.md">GAP接口</a>”</span><span id="ph17130141411612"><a name="ph17130141411612"></a><a name="ph17130141411612"></a>的</span>“<a href="注意事项.md">注意事项</a>”小节内容。</li><li>更新<span id="ph14721192481613"><a name="ph14721192481613"></a><a name="ph14721192481613"></a>“<a href="GATT-server接口.md">GATT server接口</a>”</span><span id="ph572110240164"><a name="ph572110240164"></a><a name="ph572110240164"></a>的</span>“<a href="开发流程-2.md">开发流程</a>”小节内容。</li></ul>
</td>
</tr>
<tr id="row015091213269"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p171517122269"><a name="p171517122269"></a><a name="p171517122269"></a>03</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p2151141212616"><a name="p2151141212616"></a><a name="p2151141212616"></a>2024-09-13</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><a name="ul63177346526"></a><a name="ul63177346526"></a><ul id="ul63177346526"><li>更新“<a href="概述.md">概述</a>”小节内容。</li><li>更新<span id="ph5299104310129"><a name="ph5299104310129"></a><a name="ph5299104310129"></a>“<a href="GAP接口.md">GAP接口</a>”</span><span id="ph01504513123"><a name="ph01504513123"></a><a name="ph01504513123"></a>的</span>“<a href="开发流程.md">开发流程</a>”小节内容。</li><li>更新<span id="ph79923514156"><a name="ph79923514156"></a><a name="ph79923514156"></a>“<a href="GATT-server接口.md">GATT server接口</a>”</span><span id="ph12992175119153"><a name="ph12992175119153"></a><a name="ph12992175119153"></a>的</span>“<a href="开发流程-2.md">开发流程</a>”小节内容。</li></ul>
</td>
</tr>
<tr id="row138541051205419"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p1985575112540"><a name="p1985575112540"></a><a name="p1985575112540"></a>02</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p128558517542"><a name="p128558517542"></a><a name="p128558517542"></a>2024-08-02</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p84513580549"><a name="p84513580549"></a><a name="p84513580549"></a>更新<span id="ph24694414169"><a name="ph24694414169"></a><a name="ph24694414169"></a>“<a href="GAP接口.md">GAP接口</a>”</span><span id="ph15469114111612"><a name="ph15469114111612"></a><a name="ph15469114111612"></a>的</span>“<a href="开发流程.md">开发流程</a>”小节内容。</p>
</td>
</tr>
<tr id="row1194513119152"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p127628371694"><a name="p127628371694"></a><a name="p127628371694"></a>01</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p14762133718911"><a name="p14762133718911"></a><a name="p14762133718911"></a>2024-05-15</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p169114261115"><a name="p169114261115"></a><a name="p169114261115"></a>第一次正式版本发布。</p>
</td>
</tr>
<tr id="row164134010426"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p04184044210"><a name="p04184044210"></a><a name="p04184044210"></a>00B02</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p341840194210"><a name="p341840194210"></a><a name="p341840194210"></a>2024-01-08</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p1441340114214"><a name="p1441340114214"></a><a name="p1441340114214"></a>更新<span id="ph195611601616"><a name="ph195611601616"></a><a name="ph195611601616"></a>“<a href="GAP接口.md">GAP接口</a>”</span><span id="ph14561567161"><a name="ph14561567161"></a><a name="ph14561567161"></a>的</span>“<a href="注意事项.md">注意事项</a>”小节内容。</p>
</td>
</tr>
<tr id="row528931342314"><td class="cellrowborder" valign="top" width="19.009999999999998%" headers="mcps1.1.4.1.1 "><p id="p1685385611545"><a name="p1685385611545"></a><a name="p1685385611545"></a>00B01</p>
</td>
<td class="cellrowborder" valign="top" width="25.629999999999995%" headers="mcps1.1.4.1.2 "><p id="p10853156175418"><a name="p10853156175418"></a><a name="p10853156175418"></a>2023-09-27</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p3853115665417"><a name="p3853115665417"></a><a name="p3853115665417"></a>第一次临时版本发布。</p>
</td>
</tr>
</tbody>
</table>

# 概述<a name="ZH-CN_TOPIC_0000001713023229"></a>

BS2XV100通过API（Application Programming Interface）面向开发者提供BLE功能的开发和应用接口，包括GAP、GATT server和GATT client接口。

各组件功能说明如下：

-   GAP：通用访问协议（Generic Access Profile），包含蓝牙本地设置和低功耗蓝牙的发现和连接接口。
-   GATT：通用属性协议（Generic Attribute Profile），包含服务注册、服务发现等功能相关接口。

>![](public_sys-resources/icon-note.gif) **说明：** 
>该文档描述各个模块功能的基本流程和API接口描述。

**返回值<a name="section917mcpsimp"></a>**

错误码返回值如[表 错误码](#table9501182016504)所示。

**表 1**  错误码

<a name="table9501182016504"></a>
<table><thead align="left"><tr id="row950292085010"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p25029205503"><a name="p25029205503"></a><a name="p25029205503"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="40.38%" id="mcps1.*******"><p id="p1350272085016"><a name="p1350272085016"></a><a name="p1350272085016"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="14.180000000000001%" id="mcps1.*******"><p id="p19502152005012"><a name="p19502152005012"></a><a name="p19502152005012"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="36.44%" id="mcps1.*******"><p id="p950262016502"><a name="p950262016502"></a><a name="p950262016502"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row10502132020509"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p750232017508"><a name="p750232017508"></a><a name="p750232017508"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p18498155412506"><a name="p18498155412506"></a><a name="p18498155412506"></a>ERRCODE_BT_SUCCESS</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1350242035012"><a name="p1350242035012"></a><a name="p1350242035012"></a>0x0</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p2478152273213"><a name="p2478152273213"></a><a name="p2478152273213"></a>执行成功错误码。</p>
</td>
</tr>
<tr id="row950211209505"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p75021320195017"><a name="p75021320195017"></a><a name="p75021320195017"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p2111536193217"><a name="p2111536193217"></a><a name="p2111536193217"></a>ERRCODE_BT_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p18844930141915"><a name="p18844930141915"></a><a name="p18844930141915"></a>0x80006000</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p2580741135211"><a name="p2580741135211"></a><a name="p2580741135211"></a>执行失败错误码。</p>
</td>
</tr>
<tr id="row647004635216"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p9470164695211"><a name="p9470164695211"></a><a name="p9470164695211"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p7650184023216"><a name="p7650184023216"></a><a name="p7650184023216"></a>ERRCODE_BT_NOT_READY</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p468234818198"><a name="p468234818198"></a><a name="p468234818198"></a>0x80006001</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p349816514539"><a name="p349816514539"></a><a name="p349816514539"></a>执行状态未就绪错误码。</p>
</td>
</tr>
<tr id="row204291051145212"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p19429451115217"><a name="p19429451115217"></a><a name="p19429451115217"></a>4</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p1219764620323"><a name="p1219764620323"></a><a name="p1219764620323"></a>ERRCODE_BT_MALLOC_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1240835291913"><a name="p1240835291913"></a><a name="p1240835291913"></a>0x80006002</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p14794161165319"><a name="p14794161165319"></a><a name="p14794161165319"></a>内存不足错误码。</p>
</td>
</tr>
<tr id="row208504495415"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p485544155412"><a name="p485544155412"></a><a name="p485544155412"></a>5</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p133551553153217"><a name="p133551553153217"></a><a name="p133551553153217"></a>ERRCODE_BT_MEMCPY_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p2081210583198"><a name="p2081210583198"></a><a name="p2081210583198"></a>0x80006003</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p107082179351"><a name="p107082179351"></a><a name="p107082179351"></a>内存拷贝错误错误码。</p>
</td>
</tr>
<tr id="row2914115711542"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p15914145716545"><a name="p15914145716545"></a><a name="p15914145716545"></a>6</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p157970581328"><a name="p157970581328"></a><a name="p157970581328"></a>ERRCODE_BT_BUSY</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p284313310204"><a name="p284313310204"></a><a name="p284313310204"></a>0x80006004</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p925873013511"><a name="p925873013511"></a><a name="p925873013511"></a>繁忙无法响应错误码。</p>
</td>
</tr>
<tr id="row13726122553"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p187261621559"><a name="p187261621559"></a><a name="p187261621559"></a>7</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p7620448337"><a name="p7620448337"></a><a name="p7620448337"></a>ERRCODE_BT_DONE</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1418517752012"><a name="p1418517752012"></a><a name="p1418517752012"></a>0x80006005</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1650784303513"><a name="p1650784303513"></a><a name="p1650784303513"></a>执行完成错误码。</p>
</td>
</tr>
<tr id="row119961302554"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1799683013557"><a name="p1799683013557"></a><a name="p1799683013557"></a>8</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p1710101323311"><a name="p1710101323311"></a><a name="p1710101323311"></a>ERRCODE_BT_UNSUPPORTED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p19961530125516"><a name="p19961530125516"></a><a name="p19961530125516"></a>0x80006006</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p795215509352"><a name="p795215509352"></a><a name="p795215509352"></a>不支持错误码。</p>
</td>
</tr>
<tr id="row121607340559"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p4160934185512"><a name="p4160934185512"></a><a name="p4160934185512"></a>9</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p98977185333"><a name="p98977185333"></a><a name="p98977185333"></a>ERRCODE_BT_PARAM_ERR</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p06980218200"><a name="p06980218200"></a><a name="p06980218200"></a>0x80006007</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p03951456183511"><a name="p03951456183511"></a><a name="p03951456183511"></a>无效参数错误码。</p>
</td>
</tr>
<tr id="row13618338115517"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3618103810553"><a name="p3618103810553"></a><a name="p3618103810553"></a>10</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p319202383312"><a name="p319202383312"></a><a name="p319202383312"></a>ERRCODE_BT_STATE_ERR</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1540624162015"><a name="p1540624162015"></a><a name="p1540624162015"></a>0x80006008</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p133310414365"><a name="p133310414365"></a><a name="p133310414365"></a>状态错误。</p>
</td>
</tr>
<tr id="row179331321145612"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1793472111566"><a name="p1793472111566"></a><a name="p1793472111566"></a>11</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p173181728103318"><a name="p173181728103318"></a><a name="p173181728103318"></a>ERRCODE_BT_UNHANDLED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p4651228182018"><a name="p4651228182018"></a><a name="p4651228182018"></a>0x80006009</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1738191073619"><a name="p1738191073619"></a><a name="p1738191073619"></a>未处理错误码。</p>
</td>
</tr>
<tr id="row24341235135618"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3434635155618"><a name="p3434635155618"></a><a name="p3434635155618"></a>12</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p471003516331"><a name="p471003516331"></a><a name="p471003516331"></a>ERRCODE_BT_AUTH_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1428103110205"><a name="p1428103110205"></a><a name="p1428103110205"></a>0x8000600A</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p154731715123613"><a name="p154731715123613"></a><a name="p154731715123613"></a>鉴权失败错误码。</p>
</td>
</tr>
<tr id="row625216013410"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p12252200193410"><a name="p12252200193410"></a><a name="p12252200193410"></a>13</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p205407973418"><a name="p205407973418"></a><a name="p205407973418"></a>ERRCODE_BT_RMT_DEV_DOWN</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1922953462014"><a name="p1922953462014"></a><a name="p1922953462014"></a>0x8000600B</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p20720192013616"><a name="p20720192013616"></a><a name="p20720192013616"></a>远端设备关闭错误码。</p>
</td>
</tr>
<tr id="row22071149346"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p132076412341"><a name="p132076412341"></a><a name="p132076412341"></a>14</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p04791421153417"><a name="p04791421153417"></a><a name="p04791421153417"></a>ERRCODE_BT_AUTH_REJECTED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p18893163962012"><a name="p18893163962012"></a><a name="p18893163962012"></a>0x8000600C</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p14652182523613"><a name="p14652182523613"></a><a name="p14652182523613"></a>鉴权被拒错误码。</p>
</td>
</tr>
<tr id="row1717221410424"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1563114022511"><a name="p1563114022511"></a><a name="p1563114022511"></a>15</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p1552905012251"><a name="p1552905012251"></a><a name="p1552905012251"></a>ERRCODE_BT_KEY_MISSING</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p0740157102518"><a name="p0740157102518"></a><a name="p0740157102518"></a>0x8000600F</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p132286202614"><a name="p132286202614"></a><a name="p132286202614"></a>密钥丢失错误码</p>
</td>
</tr>
</tbody>
</table>

**断连原因<a name="section4211581252"></a>**

异常断连原因如[表2](#table798293013265)所示：

**表 2**  断连原因

<a name="table798293013265"></a>
<table><thead align="left"><tr id="row89821306266"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p6982103015264"><a name="p6982103015264"></a><a name="p6982103015264"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="42.26%" id="mcps1.*******"><p id="p14982153018263"><a name="p14982153018263"></a><a name="p14982153018263"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="15.53%" id="mcps1.*******"><p id="p698218302261"><a name="p698218302261"></a><a name="p698218302261"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="33.21%" id="mcps1.*******"><p id="p998213305265"><a name="p998213305265"></a><a name="p998213305265"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row15982630162618"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1982123062610"><a name="p1982123062610"></a><a name="p1982123062610"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p19982153016266"><a name="p19982153016266"></a><a name="p19982153016266"></a>BLE_DISCONNECT_UNKNOWN</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p1498243019268"><a name="p1498243019268"></a><a name="p1498243019268"></a>0x00</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p20982113092610"><a name="p20982113092610"></a><a name="p20982113092610"></a>未知原因断连</p>
</td>
</tr>
<tr id="row17982030162611"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p10982193013264"><a name="p10982193013264"></a><a name="p10982193013264"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p6982103032614"><a name="p6982103032614"></a><a name="p6982103032614"></a>BLE_DISCONNECT_BY_PIN_OR_KEY_MISSING</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p18982193022613"><a name="p18982193022613"></a><a name="p18982193022613"></a>0x06</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p898293032612"><a name="p898293032612"></a><a name="p898293032612"></a>pin或key丢失导致断连</p>
</td>
</tr>
<tr id="row1698218303265"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p159827309269"><a name="p159827309269"></a><a name="p159827309269"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p13286461283"><a name="p13286461283"></a><a name="p13286461283"></a>BLE_DISCONNECT_BY_CONNECT_TIMEOUT</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p8982143014269"><a name="p8982143014269"></a><a name="p8982143014269"></a>0x08</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p10982230192617"><a name="p10982230192617"></a><a name="p10982230192617"></a>连接超时断连</p>
</td>
</tr>
<tr id="row1398283016261"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p2098233015263"><a name="p2098233015263"></a><a name="p2098233015263"></a>4</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p142071215294"><a name="p142071215294"></a><a name="p142071215294"></a>BLE_DISCONNECT_BY_REMOTE_USER</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p12982930122613"><a name="p12982930122613"></a><a name="p12982930122613"></a>0x13</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p1198243017265"><a name="p1198243017265"></a><a name="p1198243017265"></a>远端用户断连</p>
</td>
</tr>
<tr id="row59821308265"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p498311304267"><a name="p498311304267"></a><a name="p498311304267"></a>5</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p18276153012291"><a name="p18276153012291"></a><a name="p18276153012291"></a>BLE_DISCONNECT_BY_LOCAL_HOST</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p49831830192616"><a name="p49831830192616"></a><a name="p49831830192616"></a>0x16</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p1598311305260"><a name="p1598311305260"></a><a name="p1598311305260"></a>本端host断连。</p>
</td>
</tr>
<tr id="row10983130102614"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p159838307269"><a name="p159838307269"></a><a name="p159838307269"></a>6</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p118561446172916"><a name="p118561446172916"></a><a name="p118561446172916"></a>BLE_DISCONNECT_BY_MIC_ERROR</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p10983930112618"><a name="p10983930112618"></a><a name="p10983930112618"></a>0x3D</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p1798318305261"><a name="p1798318305261"></a><a name="p1798318305261"></a>MIC error 断连</p>
</td>
</tr>
<tr id="row49831930202618"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1698363022611"><a name="p1698363022611"></a><a name="p1698363022611"></a>7</p>
</td>
<td class="cellrowborder" valign="top" width="42.26%" headers="mcps1.******* "><p id="p181014198308"><a name="p181014198308"></a><a name="p181014198308"></a>BLE_ESTABLISH_CONNECT_FAIL</p>
</td>
<td class="cellrowborder" valign="top" width="15.53%" headers="mcps1.******* "><p id="p3983430162616"><a name="p3983430162616"></a><a name="p3983430162616"></a>0x3E</p>
</td>
<td class="cellrowborder" valign="top" width="33.21%" headers="mcps1.******* "><p id="p798313082616"><a name="p798313082616"></a><a name="p798313082616"></a>建连异常</p>
</td>
</tr>
</tbody>
</table>

# GAP接口<a name="ZH-CN_TOPIC_0000001664983686"></a>




## 概述<a name="ZH-CN_TOPIC_0000001665143402"></a>

GAP实现蓝牙设备开关控制、设备信息管理、广播管理、主动连接和断开连接等功能。

## 开发流程<a name="ZH-CN_TOPIC_0000001713023225"></a>

**使用场景<a name="section775mcpsimp"></a>**

打开蓝牙设备开关是使用蓝牙功能的首要条件，蓝牙启动后可进行设备信息管理，包括获取与设置本地设备名称、获取本地设备地址、获取配对信息、获取远端设备名称/设备类型/接收信号强度等。

当蓝牙设备需要被动与对端设备建立连接时，可设置广播参数并启动广播等待对端连接；当蓝牙设备需要主动与对端设备建立连接时，可向对端发起主动连接；当对端地址已知时，用户可直接向对端发起主动连接；当对端地址未知时，可打开蓝牙设备的扫描功能，获取正在广播的设备信息，并向对端发起主动连接；当蓝牙设备处于连接状态时，可获取设备连接信息；当蓝牙设备不需要与对端设备保持连接时，可主动断开连接。

**功能<a name="section779mcpsimp"></a>**

GAP提供的接口如[表1](#_table213321716161)所示。

**表 1**  GAP接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="22.63%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="17.43%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="22.439999999999998%" id="mcps1.*******"><p id="p142843230222"><a name="p142843230222"></a><a name="p142843230222"></a>入参说明</p>
</th>
<th class="cellrowborder" valign="top" width="37.5%" id="mcps1.*******"><p id="p79178221244"><a name="p79178221244"></a><a name="p79178221244"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row154823161515"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1254815318154"><a name="p1254815318154"></a><a name="p1254815318154"></a>enable_ble</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p15485312159"><a name="p15485312159"></a><a name="p15485312159"></a>使能BLE。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1428412233222"><a name="p1428412233222"></a><a name="p1428412233222"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p998181701718"><a name="p998181701718"></a><a name="p998181701718"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row43102035171512"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p173111535201514"><a name="p173111535201514"></a><a name="p173111535201514"></a>disable_ble</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p183118354157"><a name="p183118354157"></a><a name="p183118354157"></a>去使能BLE。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p0284723122215"><a name="p0284723122215"></a><a name="p0284723122215"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p134171515151711"><a name="p134171515151711"></a><a name="p134171515151711"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row106094013183"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1601140131814"><a name="p1601140131814"></a><a name="p1601140131814"></a>gap_ble_set_local_addr</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1860740181818"><a name="p1860740181818"></a><a name="p1860740181818"></a>设置本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1555224842010"><a name="p1555224842010"></a><a name="p1555224842010"></a>addr 本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p3320171314172"><a name="p3320171314172"></a><a name="p3320171314172"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row804mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p806mcpsimp"><a name="p806mcpsimp"></a><a name="p806mcpsimp"></a>gap_ble_get_local_addr</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p808mcpsimp"><a name="p808mcpsimp"></a><a name="p808mcpsimp"></a>获取本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p3913161719211"><a name="p3913161719211"></a><a name="p3913161719211"></a>[out]addr 本地设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p89171622112416"><a name="p89171622112416"></a><a name="p89171622112416"></a>本地设备地址存储在入参mac中；</p>
<p id="p5422117171"><a name="p5422117171"></a><a name="p5422117171"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p811mcpsimp"><a name="p811mcpsimp"></a><a name="p811mcpsimp"></a>gap_ble_set_local_name</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>设置本地设备名称。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p528402314226"><a name="p528402314226"></a><a name="p528402314226"></a>local_name：本地设备名称指针；</p>
<p id="p8164154025813"><a name="p8164154025813"></a><a name="p8164154025813"></a>length：本地设备名称长度(最大值：32)。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p11816872176"><a name="p11816872176"></a><a name="p11816872176"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1237117433014"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p174867712301"><a name="p174867712301"></a><a name="p174867712301"></a>gap_ble_set_local_appearance</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1357323616301"><a name="p1357323616301"></a><a name="p1357323616301"></a>设置本地设备appearance。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1869615499305"><a name="p1869615499305"></a><a name="p1869615499305"></a>appearance：本地设备外貌（参考对外头文件gap_ble_appearance_type_t）</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p137118415303"><a name="p137118415303"></a><a name="p137118415303"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row814mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p816mcpsimp"><a name="p816mcpsimp"></a><a name="p816mcpsimp"></a>gap_ble_get_local_name</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p818mcpsimp"><a name="p818mcpsimp"></a><a name="p818mcpsimp"></a>获取本地设备名称。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p45663117017"><a name="p45663117017"></a><a name="p45663117017"></a>local_name：本地设备名称指针；</p>
<p id="p1956616117018"><a name="p1956616117018"></a><a name="p1956616117018"></a>length：本地设备名称长度。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p881455319012"><a name="p881455319012"></a><a name="p881455319012"></a>本地设备名称存储在入参local_name中；</p>
<p id="p4301769174"><a name="p4301769174"></a><a name="p4301769174"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row188161146102212"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p9816114617222"><a name="p9816114617222"></a><a name="p9816114617222"></a>gap_ble_get_paired_devices_num</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p118162046122215"><a name="p118162046122215"></a><a name="p118162046122215"></a>获取BLE配对设备数量。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p12284132313221"><a name="p12284132313221"></a><a name="p12284132313221"></a>number：配对设备数量指针。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p20917122220244"><a name="p20917122220244"></a><a name="p20917122220244"></a>配对设备数量存储在入参number中；</p>
<p id="p748344111158"><a name="p748344111158"></a><a name="p748344111158"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row112552812172"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p8804165615175"><a name="p8804165615175"></a><a name="p8804165615175"></a>gap_ble_get_paired_devices</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p6125628121712"><a name="p6125628121712"></a><a name="p6125628121712"></a>获取BLE配对设备。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p161256282178"><a name="p161256282178"></a><a name="p161256282178"></a>number：配对设备数量指针；</p>
<p id="p842192641920"><a name="p842192641920"></a><a name="p842192641920"></a>addr：配对设备地址指针。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p18599184719186"><a name="p18599184719186"></a><a name="p18599184719186"></a>配对设备数量存储在入参number中；</p>
<p id="p10414125015189"><a name="p10414125015189"></a><a name="p10414125015189"></a>配对设备地址存储在入参addr中；</p>
<p id="p159918479188"><a name="p159918479188"></a><a name="p159918479188"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row2551202816249"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p17551202852414"><a name="p17551202852414"></a><a name="p17551202852414"></a>gap_ble_get_pair_state</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p555172820245"><a name="p555172820245"></a><a name="p555172820245"></a>获取BLE设备的配对状态。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p162845235226"><a name="p162845235226"></a><a name="p162845235226"></a>addr：对端设备地址。</p>
<p id="p2018313819151"><a name="p2018313819151"></a><a name="p2018313819151"></a>[out] status 配对设备状态.</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p491716229246"><a name="p491716229246"></a><a name="p491716229246"></a>接口返回值：配对状态（GAP_PAIR_NONE、GAP_PAIR_PAIRING、GAP_PAIR_PAIRED）。</p>
</td>
</tr>
<tr id="row849mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p851mcpsimp"><a name="p851mcpsimp"></a><a name="p851mcpsimp"></a>gap_ble_remove_all_pairs</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p853mcpsimp"><a name="p853mcpsimp"></a><a name="p853mcpsimp"></a>删除所有配对设备。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p92847239227"><a name="p92847239227"></a><a name="p92847239227"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p11656223121520"><a name="p11656223121520"></a><a name="p11656223121520"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row20610114410113"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p56604814128"><a name="p56604814128"></a><a name="p56604814128"></a>gap_ble_remove_pair</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p6610644141120"><a name="p6610644141120"></a><a name="p6610644141120"></a>删除配对设备</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p8610194410119"><a name="p8610194410119"></a><a name="p8610194410119"></a>addr：配对设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p1761016446114"><a name="p1761016446114"></a><a name="p1761016446114"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row2230111918304"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1823018191305"><a name="p1823018191305"></a><a name="p1823018191305"></a>gap_ble_disconnect_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p9230141919302"><a name="p9230141919302"></a><a name="p9230141919302"></a>断开BLE设备连接。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1285172311220"><a name="p1285172311220"></a><a name="p1285172311220"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p468122515159"><a name="p468122515159"></a><a name="p468122515159"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row16870671321"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p787057153215"><a name="p787057153215"></a><a name="p787057153215"></a>gap_ble_connect_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p188701675327"><a name="p188701675327"></a><a name="p188701675327"></a>与设备建立ACL连接。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p19431172912268"><a name="p19431172912268"></a><a name="p19431172912268"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p1924122951515"><a name="p1924122951515"></a><a name="p1924122951515"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row62279242219"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p0336232223"><a name="p0336232223"></a><a name="p0336232223"></a>gap_ble_pair_remote_device</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p222832162219"><a name="p222832162219"></a><a name="p222832162219"></a>与已连接设备进行配对。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1863761813238"><a name="p1863761813238"></a><a name="p1863761813238"></a>addr：对端设备地址。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p172286222212"><a name="p172286222212"></a><a name="p172286222212"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row413349321"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1174339183214"><a name="p1174339183214"></a><a name="p1174339183214"></a>gap_ble_connect_param_update</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1911534123219"><a name="p1911534123219"></a><a name="p1911534123219"></a>连接参数更新。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p389851913336"><a name="p389851913336"></a><a name="p389851913336"></a>params：待更新连接参数。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p6106175183319"><a name="p6106175183319"></a><a name="p6106175183319"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1966111414414"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p9966181418448"><a name="p9966181418448"></a><a name="p9966181418448"></a>gap_ble_set_adv_data</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p396661444410"><a name="p396661444410"></a><a name="p396661444410"></a>设置BLE广播数据。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p928562315224"><a name="p928562315224"></a><a name="p928562315224"></a>adv_id：广播id；</p>
<p id="p130017122280"><a name="p130017122280"></a><a name="p130017122280"></a>data：设备的广播数据（长度＜31时为普通广播，＞31时为拓展广播）。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p39171422162415"><a name="p39171422162415"></a><a name="p39171422162415"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row385983419260"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p47639526268"><a name="p47639526268"></a><a name="p47639526268"></a>gap_ble_set_adv_param</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p6859203412265"><a name="p6859203412265"></a><a name="p6859203412265"></a>设置广播参数。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p08421271272"><a name="p08421271272"></a><a name="p08421271272"></a>adv_id：广播id；</p>
<p id="p158422762716"><a name="p158422762716"></a><a name="p158422762716"></a>param：设备广播的配置参数。</p>
<p id="p1489514384506"><a name="p1489514384506"></a><a name="p1489514384506"></a><strong id="b938141312474"><a name="b938141312474"></a><a name="b938141312474"></a>注：使用板端地址发送广播时，param中own_addr应设置成全0。</strong></p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p18591134152611"><a name="p18591134152611"></a><a name="p18591134152611"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row463214167448"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1063212161445"><a name="p1063212161445"></a><a name="p1063212161445"></a>gap_ble_start_adv</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p063281613449"><a name="p063281613449"></a><a name="p063281613449"></a>启动BLE广播。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p20852247192820"><a name="p20852247192820"></a><a name="p20852247192820"></a>adv_id：广播id；</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p4917142211245"><a name="p4917142211245"></a><a name="p4917142211245"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row733811810444"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1733931844419"><a name="p1733931844419"></a><a name="p1733931844419"></a>gap_ble_stop_adv</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p6339101818444"><a name="p6339101818444"></a><a name="p6339101818444"></a>停止BLE广播。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p02851023162210"><a name="p02851023162210"></a><a name="p02851023162210"></a>adv_id：广播id。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p69171022172419"><a name="p69171022172419"></a><a name="p69171022172419"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row13667723194420"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p13667223184418"><a name="p13667223184418"></a><a name="p13667223184418"></a>gap_ble_set_scan_parameters</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p3667142354414"><a name="p3667142354414"></a><a name="p3667142354414"></a>设置扫描参数。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1285723192216"><a name="p1285723192216"></a><a name="p1285723192216"></a>param：设置的扫描参数。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p139175229245"><a name="p139175229245"></a><a name="p139175229245"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1644932574418"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p13449122518444"><a name="p13449122518444"></a><a name="p13449122518444"></a>gap_ble_start_scan</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1144942564413"><a name="p1144942564413"></a><a name="p1144942564413"></a>启动扫描。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1628572392212"><a name="p1628572392212"></a><a name="p1628572392212"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p11917162262413"><a name="p11917162262413"></a><a name="p11917162262413"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row917142864715"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1171228144716"><a name="p1171228144716"></a><a name="p1171228144716"></a>gap_ble_stop_scan</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p31702834714"><a name="p31702834714"></a><a name="p31702834714"></a>停止扫描。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p2285223172218"><a name="p2285223172218"></a><a name="p2285223172218"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p16917202211245"><a name="p16917202211245"></a><a name="p16917202211245"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row2527102934710"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p552742917477"><a name="p552742917477"></a><a name="p552742917477"></a>gap_ble_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p552711297470"><a name="p552711297470"></a><a name="p552711297470"></a>注册BLE GAP回调。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p18285523152217"><a name="p18285523152217"></a><a name="p18285523152217"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p1917102232410"><a name="p1917102232410"></a><a name="p1917102232410"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1924533433413"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p43151935193415"><a name="p43151935193415"></a><a name="p43151935193415"></a>bth_ota_init</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p14245153415342"><a name="p14245153415342"></a><a name="p14245153415342"></a>初始化bth ota通道。</p>
<p id="p2988133172316"><a name="p2988133172316"></a><a name="p2988133172316"></a><strong id="b1498843312231"><a name="b1498843312231"></a><a name="b1498843312231"></a>注：在收到ble使能成功的回调之后调用。</strong></p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p19594105153515"><a name="p19594105153515"></a><a name="p19594105153515"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p28701110173518"><a name="p28701110173518"></a><a name="p28701110173518"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row15740155224414"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p10740952184410"><a name="p10740952184410"></a><a name="p10740952184410"></a>ble_update_local_latency</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p2740352184410"><a name="p2740352184410"></a><a name="p2740352184410"></a>设置本端latency。</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p874095212447"><a name="p874095212447"></a><a name="p874095212447"></a>conn_handle：连接id</p>
<p id="p206261186477"><a name="p206261186477"></a><a name="p206261186477"></a>type：参照枚举sle_update_latency_type_t</p>
<p id="p1197294784716"><a name="p1197294784716"></a><a name="p1197294784716"></a>latency：0xFFFF未更新，其他值更新latency</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p117402052194412"><a name="p117402052194412"></a><a name="p117402052194412"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row20742161517469"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1441110223463"><a name="p1441110223463"></a><a name="p1441110223463"></a><strong id="b144118226469"><a name="b144118226469"></a><a name="b144118226469"></a>gap_ble_set_data_length</strong></p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p167422015124611"><a name="p167422015124611"></a><a name="p167422015124611"></a>设置数据长度参数</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1049710516475"><a name="p1049710516475"></a><a name="p1049710516475"></a>conn_handle：连接id</p>
<p id="p447723214485"><a name="p447723214485"></a><a name="p447723214485"></a>maxtxoctets：最大字节数(27-251)</p>
<p id="p06481650194820"><a name="p06481650194820"></a><a name="p06481650194820"></a>maxtxtime：最大发送时间(328-2120)</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p8742181554613"><a name="p8742181554613"></a><a name="p8742181554613"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row91995306175"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1519983081719"><a name="p1519983081719"></a><a name="p1519983081719"></a>gap_ble_set_local_passkey</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p819910308174"><a name="p819910308174"></a><a name="p819910308174"></a>设置本地Passkey</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p4199123017170"><a name="p4199123017170"></a><a name="p4199123017170"></a>enable：是否开启使用用户自定义Passkey</p>
<p id="p01815210274"><a name="p01815210274"></a><a name="p01815210274"></a>tk：通行码值(不得＞999999)</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p1419953019173"><a name="p1419953019173"></a><a name="p1419953019173"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row875555910185"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1756959171816"><a name="p1756959171816"></a><a name="p1756959171816"></a>gap_ble_passkey_entry</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p875625917184"><a name="p875625917184"></a><a name="p875625917184"></a>通行码输入函数</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p1475625951813"><a name="p1475625951813"></a><a name="p1475625951813"></a>conn_id：连接ID</p>
<p id="p12879122111273"><a name="p12879122111273"></a><a name="p12879122111273"></a>tk：通行码值(同上)</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p4756115918183"><a name="p4756115918183"></a><a name="p4756115918183"></a>接口返回值：错误码</p>
</td>
</tr>
<tr id="row1077424043010"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p795604803017"><a name="p795604803017"></a><a name="p795604803017"></a>gap_ble_set_sec_param</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p197741740113013"><a name="p197741740113013"></a><a name="p197741740113013"></a>设置安全参数</p>
</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p74062863113"><a name="p74062863113"></a><a name="p74062863113"></a>param：安全参数</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p18868619103117"><a name="p18868619103117"></a><a name="p18868619103117"></a>接口返回值：错误码</p>
</td>
</tr>
<tr id="row2682578133"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p20761199161310"><a name="p20761199161310"></a><a name="p20761199161310"></a>ble_set_nv_pair_keys</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* ">&nbsp;&nbsp;</td>
<td class="cellrowborder" valign="top" width="22.439999999999998%" headers="mcps1.******* "><p id="p3804519142"><a name="p3804519142"></a><a name="p3804519142"></a>key：配对信息</p>
<p id="p09111138141319"><a name="p09111138141319"></a><a name="p09111138141319"></a>own_addr：本端地址</p>
<p id="p336475151419"><a name="p336475151419"></a><a name="p336475151419"></a>peer_addr：对端地址</p>
<p id="p464725491415"><a name="p464725491415"></a><a name="p464725491415"></a>index：nv中存储编号，取值0~7（包含7）</p>
</td>
<td class="cellrowborder" valign="top" width="37.5%" headers="mcps1.******* "><p id="p166823791313"><a name="p166823791313"></a><a name="p166823791313"></a>接口返回值：错误码</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

GAP开发的具体编程实例可参考application/samples/bt。

GAP开发的典型流程：

Slave：

1.  调用gap\_ble\_register\_callbacks注册用户回调函数。
2.  调用enable\_ble，打开蓝牙开关。
3.  调用gap\_ble\_set\_local\_addr，设置本地蓝牙地址。
4.  调用gap\_ble\_set\_local\_name，设置本地设备名称。
5.  调用gap\_ble\_set\_adv\_param，设置广播参数
6.  调用gap\_ble\_set\_adv\_data，设置广播数据
7.  调用gap\_ble\_start\_adv，启动广播。

Master：

1.  调用gap\_ble\_register\_callbacks注册用户回调函数。
2.  调用enable\_ble，打开蓝牙开关。
3.  调用gap\_ble\_set\_local\_addr，设置本地蓝牙地址。
4.  调用gap\_ble\_set\_local\_name，设置本地设备名称。
5.  调用gap\_ble\_set\_scan\_parameters，设置扫描参数
6.  调用gap\_ble\_start\_scan，启动扫描
7.  调用gap\_connect\_remote\_device，连接到目标设备。
8.  调用gap\_ble\_pair\_remote\_device，与目标设备配对

设置passkey：

1.  调用gap\_ble\_set\_local\_passkey设置passkey值
2.  调用**gap\_ble\_set\_sec\_param**设置安全参数
3.  调用**gap\_ble\_set\_sec\_param**设置安全参数
4.  调用gap\_ble\_passkey\_entry输入passkey

用户指定保存NV配对信息：

1.  基于auth\_complete\_cb回调获取配对信息
2.  调用**ble\_set\_nv\_pair\_keys指定编号存储配对信息至nv**

**返回值<a name="section917mcpsimp"></a>**

获取配对状态返回值如[表2](#table994mcpsimp)所示。

**表 2**  获取配对状态返回值说明

<a name="table994mcpsimp"></a>
<table><thead align="left"><tr id="row1002mcpsimp"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p1004mcpsimp"><a name="p1004mcpsimp"></a><a name="p1004mcpsimp"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="46.79%" id="mcps1.*******"><p id="p1006mcpsimp"><a name="p1006mcpsimp"></a><a name="p1006mcpsimp"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="18.459999999999997%" id="mcps1.*******"><p id="p1008mcpsimp"><a name="p1008mcpsimp"></a><a name="p1008mcpsimp"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="25.75%" id="mcps1.*******"><p id="p1010mcpsimp"><a name="p1010mcpsimp"></a><a name="p1010mcpsimp"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row1012mcpsimp"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1014mcpsimp"><a name="p1014mcpsimp"></a><a name="p1014mcpsimp"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="46.79%" headers="mcps1.******* "><p id="p1016mcpsimp"><a name="p1016mcpsimp"></a><a name="p1016mcpsimp"></a>GAP_PAIR_NONE</p>
</td>
<td class="cellrowborder" valign="top" width="18.459999999999997%" headers="mcps1.******* "><p id="p1018mcpsimp"><a name="p1018mcpsimp"></a><a name="p1018mcpsimp"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="25.75%" headers="mcps1.******* "><p id="p1020mcpsimp"><a name="p1020mcpsimp"></a><a name="p1020mcpsimp"></a>未配对。</p>
</td>
</tr>
<tr id="row1021mcpsimp"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1023mcpsimp"><a name="p1023mcpsimp"></a><a name="p1023mcpsimp"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="46.79%" headers="mcps1.******* "><p id="p1025mcpsimp"><a name="p1025mcpsimp"></a><a name="p1025mcpsimp"></a>GAP_PAIR_PAIRING</p>
</td>
<td class="cellrowborder" valign="top" width="18.459999999999997%" headers="mcps1.******* "><p id="p1027mcpsimp"><a name="p1027mcpsimp"></a><a name="p1027mcpsimp"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="25.75%" headers="mcps1.******* "><p id="p1029mcpsimp"><a name="p1029mcpsimp"></a><a name="p1029mcpsimp"></a>配对中。</p>
</td>
</tr>
<tr id="row1030mcpsimp"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1032mcpsimp"><a name="p1032mcpsimp"></a><a name="p1032mcpsimp"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="46.79%" headers="mcps1.******* "><p id="p1034mcpsimp"><a name="p1034mcpsimp"></a><a name="p1034mcpsimp"></a>GAP_PAIR_PAIRED</p>
</td>
<td class="cellrowborder" valign="top" width="18.459999999999997%" headers="mcps1.******* "><p id="p1036mcpsimp"><a name="p1036mcpsimp"></a><a name="p1036mcpsimp"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="25.75%" headers="mcps1.******* "><p id="p1038mcpsimp"><a name="p1038mcpsimp"></a><a name="p1038mcpsimp"></a>已配对。</p>
</td>
</tr>
</tbody>
</table>

## 注意事项<a name="ZH-CN_TOPIC_0000001664983690"></a>

-   本产品只作为低功耗蓝牙设备工作时，最多只支持八路低功耗蓝牙连接，同时作为低功耗蓝牙设备和星闪设备工作时，一共支持八路连接。
-   若扫描不到设备，请先检查设备是否已在配对设备列表中，或者设备是否已与其他设备配对（此情况下需要先清除设备端配对信息）。

# GATT server接口<a name="ZH-CN_TOPIC_0000001713103209"></a>



## 概述<a name="ZH-CN_TOPIC_0000001665143394"></a>

GATT是一个基于蓝牙GAP连接的发送和接收数据的通用规范，支持在两个蓝牙设备间进行数据传输。

## 开发流程<a name="ZH-CN_TOPIC_0000001713023233"></a>

**使用场景<a name="section775mcpsimp"></a>**

GATT server主要接收对端设备的命令和请求，给对端设备发送响应、指示或者通知。

**功能<a name="section779mcpsimp"></a>**

GATT server提供的接口如[表1](#_table213321716161)所示。

**表 1**  GATT server接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="15.981598159815983%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="33.093309330933096%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.562756275627564%" id="mcps1.*******"><p id="p134231719219"><a name="p134231719219"></a><a name="p134231719219"></a>入参说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p109931320124"><a name="p109931320124"></a><a name="p109931320124"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p10121164514"><a name="p10121164514"></a><a name="p10121164514"></a>gatts_register_server</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p798mcpsimp"><a name="p798mcpsimp"></a><a name="p798mcpsimp"></a>Gatt server注册。根据传入的UUID注册server，回调函数返回server接口ID。</p>
<p id="p8737184118442"><a name="p8737184118442"></a><a name="p8737184118442"></a><strong id="b938141312474"><a name="b938141312474"></a><a name="b938141312474"></a>注：目前只支持注册一个GATT server。</strong></p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p11342191712210"><a name="p11342191712210"></a><a name="p11342191712210"></a>app_uuid：应用UUID指针；</p>
<p id="p382884718520"><a name="p382884718520"></a><a name="p382884718520"></a>server_id：服务端ID指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p15940192145510"><a name="p15940192145510"></a><a name="p15940192145510"></a>服务端ID存储在server_id中；</p>
<p id="p1399310205210"><a name="p1399310205210"></a><a name="p1399310205210"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p64121559185616"><a name="p64121559185616"></a><a name="p64121559185616"></a>gatts_unregister_server</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p62365161575"><a name="p62365161575"></a><a name="p62365161575"></a>注销gatt服务端。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p23428178216"><a name="p23428178216"></a><a name="p23428178216"></a>server_id：服务器ID。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p1258284916316"><a name="p1258284916316"></a><a name="p1258284916316"></a>gatts_add_service</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>添加service。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p2488632383"><a name="p2488632383"></a><a name="p2488632383"></a>server_id：服务器ID；</p>
<p id="p39401883412"><a name="p39401883412"></a><a name="p39401883412"></a>service_uuid：服务UUID；</p>
<p id="p14894321982"><a name="p14894321982"></a><a name="p14894321982"></a>is_primary：是否是首要服务。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p580165015586"><a name="p580165015586"></a><a name="p580165015586"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row14457165322015"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p276818416415"><a name="p276818416415"></a><a name="p276818416415"></a>gatts_add_characteristic</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p5457753102011"><a name="p5457753102011"></a><a name="p5457753102011"></a>添加characteristic到指定的service。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p582817101011"><a name="p582817101011"></a><a name="p582817101011"></a>server_id：服务器ID；</p>
<p id="p160105318410"><a name="p160105318410"></a><a name="p160105318410"></a>service_handle：服务句柄；</p>
<p id="p1682817711010"><a name="p1682817711010"></a><a name="p1682817711010"></a>character：特征信息。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1321275215811"><a name="p1321275215811"></a><a name="p1321275215811"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p826mcpsimp"><a name="p826mcpsimp"></a><a name="p826mcpsimp"></a>gatts_add_descriptor</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p156731625104116"><a name="p156731625104116"></a><a name="p156731625104116"></a>添加descriptor到对应的characteristic。</p>
<p id="p828mcpsimp"><a name="p828mcpsimp"></a><a name="p828mcpsimp"></a>包含当前Characteristic的描述信息、配置信息、表示格式信息等。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p620975619104"><a name="p620975619104"></a><a name="p620975619104"></a>server_id：服务器ID；</p>
<p id="p7491721650"><a name="p7491721650"></a><a name="p7491721650"></a>service_handle：服务句柄；</p>
<p id="p62091156181015"><a name="p62091156181015"></a><a name="p62091156181015"></a>descriptor：描述符信息。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1699320201023"><a name="p1699320201023"></a><a name="p1699320201023"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1257712474513"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p21849131063"><a name="p21849131063"></a><a name="p21849131063"></a>gatts_add_service_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p13185839205810"><a name="p13185839205810"></a><a name="p13185839205810"></a>添加一个gatt服务同步接口，服务句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p44271153867"><a name="p44271153867"></a><a name="p44271153867"></a>server_id：服务器ID；</p>
<p id="p1142718531360"><a name="p1142718531360"></a><a name="p1142718531360"></a>service_uuid：服务UUID；</p>
<p id="p74271953068"><a name="p74271953068"></a><a name="p74271953068"></a>is_primary：是否是首要服务；</p>
<p id="p2447157277"><a name="p2447157277"></a><a name="p2447157277"></a>handle：服务句柄指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1196011556105"><a name="p1196011556105"></a><a name="p1196011556105"></a>服务句柄存储在handle中；</p>
<p id="p15960115591011"><a name="p15960115591011"></a><a name="p15960115591011"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row386531016"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p11881155101013"><a name="p11881155101013"></a><a name="p11881155101013"></a>gatts_add_characteristic_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p5732114055213"><a name="p5732114055213"></a><a name="p5732114055213"></a>添加一个gatt特征同步接口，特征句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p478662517101"><a name="p478662517101"></a><a name="p478662517101"></a>server_id：服务器ID；</p>
<p id="p115851813141312"><a name="p115851813141312"></a><a name="p115851813141312"></a>service_handle：服务UUID；</p>
<p id="p049137111316"><a name="p049137111316"></a><a name="p049137111316"></a>character：GATT特征；</p>
<p id="p254192418317"><a name="p254192418317"></a><a name="p254192418317"></a>result：特征句柄指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p139163983118"><a name="p139163983118"></a><a name="p139163983118"></a>特征句柄存储在</p>
<p id="p173718391310"><a name="p173718391310"></a><a name="p173718391310"></a>result中；</p>
<p id="p1315723515115"><a name="p1315723515115"></a><a name="p1315723515115"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row1913210313121"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p2092553120128"><a name="p2092553120128"></a><a name="p2092553120128"></a>gatts_add_descriptor_sync</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1180311441527"><a name="p1180311441527"></a><a name="p1180311441527"></a>添加一个gatt特征描述符同步接口，特征描述符句柄同步返回。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p153901413141411"><a name="p153901413141411"></a><a name="p153901413141411"></a>server_id：服务器ID；</p>
<p id="p3390151318144"><a name="p3390151318144"></a><a name="p3390151318144"></a>service_handle：服务UUID；</p>
<p id="p053154812810"><a name="p053154812810"></a><a name="p053154812810"></a>descriptor：特征描述符；</p>
<p id="p19390113151411"><a name="p19390113151411"></a><a name="p19390113151411"></a>handle：特征描述符句柄指针。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p670710394149"><a name="p670710394149"></a><a name="p670710394149"></a>特征描述符句柄存储在handle中；</p>
<p id="p1570763941415"><a name="p1570763941415"></a><a name="p1570763941415"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row829mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p831mcpsimp"><a name="p831mcpsimp"></a><a name="p831mcpsimp"></a>gatts_start_service</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p833mcpsimp"><a name="p833mcpsimp"></a><a name="p833mcpsimp"></a>启动service。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1332153919115"><a name="p1332153919115"></a><a name="p1332153919115"></a>server_id：服务器ID；</p>
<p id="p123610581144"><a name="p123610581144"></a><a name="p123610581144"></a>service_handle：服务句柄。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p7174165817582"><a name="p7174165817582"></a><a name="p7174165817582"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row834mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p18871356191519"><a name="p18871356191519"></a><a name="p18871356191519"></a>gatts_delete_all_services</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p838mcpsimp"><a name="p838mcpsimp"></a><a name="p838mcpsimp"></a>删除所有GATT服务。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p19815155813110"><a name="p19815155813110"></a><a name="p19815155813110"></a>server_id：服务器ID；</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p63401159185813"><a name="p63401159185813"></a><a name="p63401159185813"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row844mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p4248123871714"><a name="p4248123871714"></a><a name="p4248123871714"></a>gatts_send_response</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1230216577176"><a name="p1230216577176"></a><a name="p1230216577176"></a>用户发送响应。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p16066581212"><a name="p16066581212"></a><a name="p16066581212"></a>server_id：服务器ID；</p>
<p id="p14238121051820"><a name="p14238121051820"></a><a name="p14238121051820"></a>conn_id：连接ID；</p>
<p id="p184988291185"><a name="p184988291185"></a><a name="p184988291185"></a>param：响应参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p17545120155913"><a name="p17545120155913"></a><a name="p17545120155913"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row854mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p424242414201"><a name="p424242414201"></a><a name="p424242414201"></a>gatts_notify_indicate</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p858mcpsimp"><a name="p858mcpsimp"></a><a name="p858mcpsimp"></a>给远端client发送indication/notification。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p189329328226"><a name="p189329328226"></a><a name="p189329328226"></a>server_id：服务器ID；</p>
<p id="p1193223212210"><a name="p1193223212210"></a><a name="p1193223212210"></a>conn_id：连接ID；</p>
<p id="p129321332182214"><a name="p129321332182214"></a><a name="p129321332182214"></a>param：通知或指示参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p8852161145910"><a name="p8852161145910"></a><a name="p8852161145910"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row464916284915"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p46493282912"><a name="p46493282912"></a><a name="p46493282912"></a>gatts_notify_indicate_direct_send</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p66496281298"><a name="p66496281298"></a><a name="p66496281298"></a>向对端发送通知或指示,不更新Character值</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1681410521695"><a name="p1681410521695"></a><a name="p1681410521695"></a>server_id：服务器ID；</p>
<p id="p12814185220910"><a name="p12814185220910"></a><a name="p12814185220910"></a>conn_id：连接ID；</p>
<p id="p148143521794"><a name="p148143521794"></a><a name="p148143521794"></a>param：通知或指示参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p864911281693"><a name="p864911281693"></a><a name="p864911281693"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row93013312138"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p19560191162115"><a name="p19560191162115"></a><a name="p19560191162115"></a>gatts_notify_indicate_by_uuid</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p13301731101316"><a name="p13301731101316"></a><a name="p13301731101316"></a>按照UUID给远端client发送indication/notification。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p447113013234"><a name="p447113013234"></a><a name="p447113013234"></a>server_id：服务器ID；</p>
<p id="p7471100162316"><a name="p7471100162316"></a><a name="p7471100162316"></a>conn_id：连接ID；</p>
<p id="p1147116010237"><a name="p1147116010237"></a><a name="p1147116010237"></a>param：通知或指示参数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p665017314593"><a name="p665017314593"></a><a name="p665017314593"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row15903162713231"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p89271014242"><a name="p89271014242"></a><a name="p89271014242"></a>gatts_set_mtu_size</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p1169284912253"><a name="p1169284912253"></a><a name="p1169284912253"></a>在连接之前设置服务端接收mtu。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p16129194355418"><a name="p16129194355418"></a><a name="p16129194355418"></a>server_id：服务器ID；</p>
<p id="p833414605520"><a name="p833414605520"></a><a name="p833414605520"></a>mtu_size：服务端接收mtu值。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p2904527172315"><a name="p2904527172315"></a><a name="p2904527172315"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row859mcpsimp"><td class="cellrowborder" valign="top" width="15.981598159815983%" headers="mcps1.******* "><p id="p861mcpsimp"><a name="p861mcpsimp"></a><a name="p861mcpsimp"></a>gatts_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="33.093309330933096%" headers="mcps1.******* "><p id="p863mcpsimp"><a name="p863mcpsimp"></a><a name="p863mcpsimp"></a>注册GATT server回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p434291711216"><a name="p434291711216"></a><a name="p434291711216"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p206731868593"><a name="p206731868593"></a><a name="p206731868593"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

GATT server开发具体编程实例可参考application/samples/bt。

GATT server开发的典型流程：添加服务和特征及描述信息并启动服务。

1.  调用gatts\_register\_callbacks，注册GATT server用户回调函数。
2.  调用enable\_ble，打开蓝牙开关。
3.  调用gatts\_register\_server，创建一个server。
4.  调用gatts\_add\_service，根据UUID创建service。
5.  调用gatts\_add\_characteristic，对创建的服务添加特征值。
6.  调用gatts\_add\_descriptor，对服务中的特征添加描述信息。
7.  调用gatts\_start\_service，启动service。
8.  启动广播，等待对端连接。
9.  被对端使能为“可通知”后，调用gatts\_notify\_indicate或gatts\_notify\_indicate\_by\_uuid向对端发起特征值通知。

# GATT client接口<a name="ZH-CN_TOPIC_0000001665143398"></a>



## 概述<a name="ZH-CN_TOPIC_0000001713103217"></a>

GATT是一个基于蓝牙GAP连接的发送和接收数据的通用规范，支持在两个蓝牙设备间进行数据传输。

## 开发流程<a name="ZH-CN_TOPIC_0000001664983682"></a>

**使用场景<a name="section775mcpsimp"></a>**

GATT client主要给对端发送命令和请求，接收对端回复的响应、指示和通知。

**功能<a name="section779mcpsimp"></a>**

GATT client提供的接口如[表1](#_table213321716161)所示。

**表 1**  GATT client接口描述

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="22.932293229322934%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="22.562256225622562%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="28.922892289228923%" id="mcps1.*******"><p id="p6395659131714"><a name="p6395659131714"></a><a name="p6395659131714"></a>入参说明</p>
</th>
<th class="cellrowborder" valign="top" width="25.58255825582558%" id="mcps1.*******"><p id="p382645520174"><a name="p382645520174"></a><a name="p382645520174"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p154051511145711"><a name="p154051511145711"></a><a name="p154051511145711"></a>gattc_register_client</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p798mcpsimp"><a name="p798mcpsimp"></a><a name="p798mcpsimp"></a>注册GATT client。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p16396105919170"><a name="p16396105919170"></a><a name="p16396105919170"></a>app_uuid：应用UUID；</p>
<p id="p13865173315719"><a name="p13865173315719"></a><a name="p13865173315719"></a>client_id：客户端ID指针。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p78363135810"><a name="p78363135810"></a><a name="p78363135810"></a>客户端id存储在client_id中</p>
<p id="p1271050135716"><a name="p1271050135716"></a><a name="p1271050135716"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p6303133216246"><a name="p6303133216246"></a><a name="p6303133216246"></a>gattc_unregister_client</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p803mcpsimp"><a name="p803mcpsimp"></a><a name="p803mcpsimp"></a>取消注册GATT client。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p193967592179"><a name="p193967592179"></a><a name="p193967592179"></a>client_id：客户端ID。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p1412393035719"><a name="p1412393035719"></a><a name="p1412393035719"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row814mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p1467613819592"><a name="p1467613819592"></a><a name="p1467613819592"></a>gattc_discovery_service</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p818mcpsimp"><a name="p818mcpsimp"></a><a name="p818mcpsimp"></a>发现服务。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p1286343752016"><a name="p1286343752016"></a><a name="p1286343752016"></a>client_id：客户端ID；</p>
<p id="p58631337132020"><a name="p58631337132020"></a><a name="p58631337132020"></a>conn_id：连接ID；</p>
<p id="p715020424212"><a name="p715020424212"></a><a name="p715020424212"></a>uuid：服务UUID。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p71301651434"><a name="p71301651434"></a><a name="p71301651434"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p826mcpsimp"><a name="p826mcpsimp"></a><a name="p826mcpsimp"></a>gattc_discovery_character</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p1823011016514"><a name="p1823011016514"></a><a name="p1823011016514"></a>发现特征。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p158691452102110"><a name="p158691452102110"></a><a name="p158691452102110"></a>client_id：客户端ID；</p>
<p id="p1786965216211"><a name="p1786965216211"></a><a name="p1786965216211"></a>conn_id：连接ID；</p>
<p id="p51241210376"><a name="p51241210376"></a><a name="p51241210376"></a>param：发现的特征参数。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p1141512410714"><a name="p1141512410714"></a><a name="p1141512410714"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row187771111443"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p291311341277"><a name="p291311341277"></a><a name="p291311341277"></a>gattc_discovery_descriptor</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p24618501073"><a name="p24618501073"></a><a name="p24618501073"></a>发现特征描述符。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p1214713118818"><a name="p1214713118818"></a><a name="p1214713118818"></a>client_id：客户端ID；</p>
<p id="p614710111819"><a name="p614710111819"></a><a name="p614710111819"></a>conn_id：连接ID；</p>
<p id="p15672837681"><a name="p15672837681"></a><a name="p15672837681"></a>character_handle：特征声明句柄。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p562162514122"><a name="p562162514122"></a><a name="p562162514122"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row829mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p66931942141212"><a name="p66931942141212"></a><a name="p66931942141212"></a>gattc_read_req_by_handle</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p14629105110122"><a name="p14629105110122"></a><a name="p14629105110122"></a>发起按照句柄读取请求。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p1881181371317"><a name="p1881181371317"></a><a name="p1881181371317"></a>client_id：客户端ID；</p>
<p id="p18171313138"><a name="p18171313138"></a><a name="p18171313138"></a>conn_id：连接ID；</p>
<p id="p13850191610137"><a name="p13850191610137"></a><a name="p13850191610137"></a>handle：句柄。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p2028114503139"><a name="p2028114503139"></a><a name="p2028114503139"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row834mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p18230510183714"><a name="p18230510183714"></a><a name="p18230510183714"></a>gattc_read_req_by_uuid</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p84257525157"><a name="p84257525157"></a><a name="p84257525157"></a>发起按照UUID读取请求。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p9285139257"><a name="p9285139257"></a><a name="p9285139257"></a>client_id：客户端ID；</p>
<p id="p92856332518"><a name="p92856332518"></a><a name="p92856332518"></a>conn_id：连接ID；</p>
<p id="p8884158151614"><a name="p8884158151614"></a><a name="p8884158151614"></a>param：按照UUID读取请求参数。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p178461456191418"><a name="p178461456191418"></a><a name="p178461456191418"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row839mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p1842814138170"><a name="p1842814138170"></a><a name="p1842814138170"></a>gattc_write_req</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p18486325201715"><a name="p18486325201715"></a><a name="p18486325201715"></a>发起写请求。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p751893382519"><a name="p751893382519"></a><a name="p751893382519"></a>client_id：客户端ID；</p>
<p id="p10518633192511"><a name="p10518633192511"></a><a name="p10518633192511"></a>conn_id：连接ID；</p>
<p id="p6130181911168"><a name="p6130181911168"></a><a name="p6130181911168"></a>param：写请求参数。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p4852124711178"><a name="p4852124711178"></a><a name="p4852124711178"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row844mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p1223455811178"><a name="p1223455811178"></a><a name="p1223455811178"></a>gattc_write_cmd</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p3765131071818"><a name="p3765131071818"></a><a name="p3765131071818"></a>发起写命令。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p1479012267199"><a name="p1479012267199"></a><a name="p1479012267199"></a>client_id：客户端ID；</p>
<p id="p979062615192"><a name="p979062615192"></a><a name="p979062615192"></a>conn_id：连接ID；</p>
<p id="p19790162601914"><a name="p19790162601914"></a><a name="p19790162601914"></a>param：写命令参数。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p786201482413"><a name="p786201482413"></a><a name="p786201482413"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row01513582212"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p713615616226"><a name="p713615616226"></a><a name="p713615616226"></a>gattc_exchange_mtu_req</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p11381413192313"><a name="p11381413192313"></a><a name="p11381413192313"></a>发送交换mtu请求。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p8575533122313"><a name="p8575533122313"></a><a name="p8575533122313"></a>client_id：客户端ID；</p>
<p id="p757513332313"><a name="p757513332313"></a><a name="p757513332313"></a>conn_id：连接ID；</p>
<p id="p1791612535236"><a name="p1791612535236"></a><a name="p1791612535236"></a>mtu_size：客户端接收mtu。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p1753351518248"><a name="p1753351518248"></a><a name="p1753351518248"></a>接口返回值：错误码。</p>
</td>
</tr>
<tr id="row859mcpsimp"><td class="cellrowborder" valign="top" width="22.932293229322934%" headers="mcps1.******* "><p id="p861mcpsimp"><a name="p861mcpsimp"></a><a name="p861mcpsimp"></a>gattc_register_callbacks</p>
</td>
<td class="cellrowborder" valign="top" width="22.562256225622562%" headers="mcps1.******* "><p id="p863mcpsimp"><a name="p863mcpsimp"></a><a name="p863mcpsimp"></a>注册GATT client回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="28.922892289228923%" headers="mcps1.******* "><p id="p14396145910172"><a name="p14396145910172"></a><a name="p14396145910172"></a>func：用户回调函数。</p>
</td>
<td class="cellrowborder" valign="top" width="25.58255825582558%" headers="mcps1.******* "><p id="p1353141782410"><a name="p1353141782410"></a><a name="p1353141782410"></a>接口返回值：错误码。</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

GATT client开发的具体编程实例可参考application/samples/bt。

GATT client开发的典型流程：连接对端设备，发现对端设备的服务，读写对端特征值，订阅对端的通知或者指示。

1.  调用gattc\_register\_callbacks，注册GATT client用户回调函数。
2.  调用enable\_ble，打开蓝牙开关。
3.  调用gattc\_register\_client，创建一个client。
4.  递归调用gattc\_discovery\_service，gattc\_discovery\_character和gattc\_discovery\_descriptor，获取对端的属性数据库。
5.  调用gattc\_write\_req或gattc\_write\_cmd将关注的对端特征的客户端特征配置写为0x0001或0x0002，设置为前者时可收到关注特征的特征通知，设置为后者时可收到关注特征的特征指示。
6.  调用相应读写接口操作GATT server的特征和描述符。

