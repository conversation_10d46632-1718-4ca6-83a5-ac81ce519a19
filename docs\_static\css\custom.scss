@import url(../iconfont/iconfont.css);

*,
:after,
:before {
  box-sizing: inherit;
}

.ellipse {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body {
  height: 100%;
  background-color: unset;

  .wy-grid-for-nav {
    display: flex;
    height: 100%;

    .wy-nav-side {
      position: relative;
      width: 360px;
      background: #F3F3F3;
      color: #777;
      padding: 0;


      .wy-side-scroll {
        padding: 0;
        width: 100%;
        overflow-x: hidden;
        overflow-y: hidden;
        display: flex;
        flex-direction: column;


        .wy-side-nav-search {
          width: 100%;
          background: unset;
          padding: 0;
          color: inherit;
          margin-bottom: 0;
          text-align: left;

          &>a {
            display: block;
            padding: 0;
            margin: 0;


            &:before {
              content: none;
            }

            img {
              margin: 0 auto;
              padding: 0;
              margin-top: 32px;
              margin-bottom: 24px;
              width: 194px;
              height: 48px;
            }

            .projectTitle {
              color: #000;
              text-align: center;
            }

            &:hover {
              background: unset;

            }
          }

          .projectTitle {
            font-weight: bold;
            font-size: 20px;
            line-height: 30px;
            margin-bottom: 24px;
          }

          .selectors {
            margin: 36px 24px 24px 24px;
            display: flex;
            justify-content: space-between;

            .repo-readOnly {
              font-weight: bold;
              font-size: 20px;
              color: #191919;
              max-width: 124px;

            }

            .version-readOnly {
              font-size: 12px;
              line-height: 18px;
              color: #191919;
              max-width: 84px;
            }

            input {
              border: 0;
              color: #191919;
              background-color: inherit;
              box-shadow: none;
              padding: 0;
              flex-shrink: 0;
              border-radius: 0;
              z-index: -1;

              &#repo-select {
                font-weight: bold;
                font-size: 20px;
                width: 0;
              }

              &#version-select {
                width: 0;

              }
            }

            .select {
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 4px;

              .icon-xiala {
                transition: all 0.1s;

              }

              &.active {
                .icon-xiala {
                  transform: rotate(180deg);
                }
              }
            }

            .repo-select,
            .version-select {
              position: relative;
              display: flex;
              align-items: center;
              flex: 1;
              width: 100%;

              .text {
                font-size: 12px;
                flex-shrink: 0;
              }
            }

            .version-select {
              justify-content: flex-end;

              &.only-version {
                justify-content: flex-start;

                .text {
                  font-size: 14px;
                  line-height: 22px;
                }

                .version-readOnly {
                  font-size: 16px;
                  line-height: 24px;
                  font-weight: bold;
                  max-width: 242px;
                }
              }
            }

            #repo-options,
            #version-options {
              max-width: 312px;
              position: absolute;
              top: 24px;
              background: #FFF;
              z-index: 1;
              padding: 4px;
              color: #191919;
              border-radius: 8px;
              display: none;

              .opTitle {
                color: #c3c3c3;
                padding: 4px 8px;
                font-size: 12px;
              }

              .option {
                font-size: 14px;
                line-height: 22px;
                border-radius: 4px;
                padding: 7px 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: pointer;

                &:hover {
                  background-color: #e1e8fd;
                }
              }
            }

            #version-options {
              min-width: 212px;
            }
          }


          .wy-form {
            padding: 0;
            margin: 0 24px;
            padding-top: 24px;
            border-top: 1px solid #dfdfdf;
            position: relative;

            &::after {
              font-family: "iconfont";
              content: '\e690';
              font-size: 14px;
              position: absolute;
              top: 33px;
              left: 12px;

            }

            input[type=text] {
              border-radius: 8px;
              border: 1px solid #c9c9c9;
              transition: all 0.1s;
              position: relative;
              padding-left: 34px;
              height: 32px;
              font-size: 14px;


              &:hover {
                border-color: #5e7ce0;
              }
            }
          }



        }

        .wy-menu-vertical {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex: 1;
          padding: 12px 24px 30px 24px;



          ul {
            width: 100%;
            overflow: auto;
            scroll-behavior: smooth;

            &::-webkit-scrollbar {
              width: 6px;
            }

            /* Track */
            &::-webkit-scrollbar-track {
              -webkit-border-radius: 4px;
              border-radius: 4px;
            }

            /* Handle */
            &::-webkit-scrollbar-thumb {
              -webkit-border-radius: 4px;
              border-radius: 4px;
              background: #dfdfdf;
            }

            li {
              background: inherit;
              color: inherit;

              a {
                border: 0;
                border-radius: 8px;
                color: inherit;
                background: inherit;
                transition: all 0.1s;

                button {
                  color: inherit;
                }
              }

              button.toctree-expand::before {
                font-family: 'iconfont';
                content: '\e696';
              }

              &.current>a {
                font-weight: inherit;
                color: inherit;
                background-color: inherit;

                button.toctree-expand::before {
                  font-family: 'iconfont';
                  transform: rotate(90deg);
                  content: '\e696';
                }
              }



              a:hover {
                background: rgba($color: #191919, $alpha: 0.05);
                color: #191919;
                font-weight: bold;

              }

              a.current {
                background: #e1e8fd;
                color: #4965c3;
                font-weight: bold;

              }

            }
          }
        }
      }
    }

    .wy-nav-content-wrap {
      margin: 0;
      position: relative;
      width: calc(100% - 360px);
      height: 100%;
      background-color: #fff;

      .wy-nav-content {
        max-width: unset;
        width: 100%;
        overflow: auto;
        padding: 60px;
        scroll-behavior: smooth;
        background: #fff;

        .rst-content {
          height: 100%;
          display: flex;
          flex-direction: column;

          div[role=navigation] {
            padding-bottom: 32px;
            border-bottom: 1px solid #c9c9c9;
            margin-bottom: 8px;

            hr {
              display: none;
            }
          }

          .wy-breadcrumbs {
            color: #777;
            font-size: 12px;

            .icon-home {
              font-family: 'iconfont';

              &:before {
                font-family: inherit;
                content: '\e68c'
              }
            }

            li.active {
              color: #191919;
            }

            a {
              color: #777;
            }

            .wy-breadcrumbs-aside {

              .fa-github {
                color: #4965c3;
                display: flex;
                align-content: center;
                line-height: 24px;

                .HiSpark {
                  width: 24px;
                  height: 24px;
                  margin-right: 8px;
                }

                &::before {
                  content: none;
                }
              }


            }
          }

          .document {
            flex: 1;

            blockquote,
            figure,
            form,
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              margin: 16px 0;
              font-weight: normal;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
              margin-top: 24px;
              font-weight: bold;
            }

            h1 {
              font-size: 32px;
              line-height: 48px;
            }

            h2 {
              font-size: 28px;
              line-height: 48px;
            }

            h3 {
              font-size: 24px;
              line-height: 36px;
            }

            h4 {
              font-size: 20px;
              line-height: 30px;
            }

            h5 {
              font-size: 16px;
              line-height: 24px;
            }

            h6 {
              font-size: 12px;
              line-height: 20px;
            }

            a {
              color: #5e7ce0;
            }

            p {
              font-size: 14px;
              line-height: 22px;
            }

            table {

              th,
              td {
                padding: 7px 16px;
                border: 1px solid #dfdfdf;
              }

              p {
                padding: 0;
                margin: 0;
              }

              th p {
                font-weight: bold;
              }
            }

            .headerlink {
              color: transparent;
              user-select: none;

              &::before {
                font-family: 'iconfont';
                content: '\e697';
                color: #7693f5;
              }
            }
          }

          footer {
            margin-top: 32px;
            color: #777;

            hr {
              display: none;
            }

            .feedback {
              background-color: transparent;
            }

            .alert {
              padding: 28px 32px;
              color: #191919;
              box-shadow: 0 16px 48px rgba($color: #000000, $alpha: 0.16);
              width: 920px;
              min-width: none;
              max-width: none;
              flex-direction: column;


              .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 16px;
                line-height: 24px;

                .title {
                  font-weight: bold;
                }

                .icon-close {
                  position: static;
                  background: none;

                }
              }

              .content {
                margin-top: 24px;
                flex: 1;
                display: flex;
                width: 100%;
                overflow: auto;
                flex-direction: column;

                form {
                  margin: 0;
                  display: flex;
                  flex-direction: column;
                  gap: 16px;
                  width: 100%;

                  .form-item {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .form-item-label {
                      position: relative;
                      font-size: 14px;
                      line-height: 22px;

                      &.required {
                        &::before {
                          content: '* ';
                          color: #e7625d;
                        }
                      }
                    }

                    .textarea-box {
                      margin: 0;
                      min-height: unset;

                      textarea {
                        padding: 5px 12px;
                        min-height: 132px;
                        border: 1px solid #c9c9c9;
                        border-radius: 4px;
                        box-shadow: none;
                        transition: all 0.1s;
                        font-size: 14px;
                        line-height: 22px;

                        &:hover,
                        &:focus {
                          border-color: #5e7ce0;
                        }
                      }
                    }

                    .switch {
                      background: #F3F3F3;
                      border-radius: 6px;
                      padding: 2px;
                      display: inline-flex;
                      gap: 4px;

                      .switch-item {
                        border: 0;
                        border-radius: 4px;
                        padding: 3px 16px;
                        background-color: inherit;
                        color: #777;
                        line-height: 22px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        cursor: pointer;
                        user-select: none;

                        &.selected {
                          background: #fff;
                          color: #5e7ce0;
                          font-weight: bold;
                          box-shadow: 0 1px 6px rgba($color: #000000, $alpha: 0.08);
                        }
                      }
                    }
                  }
                }
              }

              .footer {
                margin-top: 24px;
                display: flex;
                justify-content: flex-end;
                gap: 8px;

                button {
                  appearance: none;
                  border: 1px solid #c9c9c9;
                  border-radius: 4px;
                  background: #fff;
                  color: #191919;
                  padding: 5px 30px;
                  transition: all 0.3s;

                  &:hover {
                    border-color: #191919;
                  }

                  &:active {
                    border-color: #5e7ce0;
                  }

                  &.btn-submit {
                    margin: 0;
                    font-size: unset;
                    line-height: unset;
                    height: unset;
                    width: unset;

                  }

                  &.primary {
                    background-color: #5e7ce0;
                    color: #fff;
                    border: 0;

                    &:hover {
                      background-color: #7693f5;
                    }

                    &:active {
                      background-color: #465eb8;
                    }

                  }
                }

              }
            }

            .feedback-content {
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #5e7ce0;
              border-radius: 8px;
              user-select: none;
              transition: all 0.3s;

              .iconfont {
                color: #fff;
              }

              &:hover {
                background-color: #7693f5;
              }

              &:active {
                background-color: #465eb8;
              }
            }

            .rst-footer-buttons {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-bottom: 0;
              margin-bottom: 25px;
              padding-bottom: 0;

              &::before,
              &::after {
                content: none;
              }

              .float-left,
              .float-right {
                float: none;
              }

              a {
                background: inherit !important;
                color: inherit !important;
                border: 0;
                border-radius: 16px;
                padding: 5px 16px;
                box-shadow: none;
                font-size: 14px;
                line-height: 22px;
                display: flex;
                align-items: center;
                gap: 8px;

                .fa-arrow-circle-right::before,
                .fa-arrow-circle-left::before {
                  font-size: 16px;
                  line-height: 22px;
                  font-family: 'iconfont';
                }

                .fa-arrow-circle-left::before {
                  content: '\e692';
                }

                .fa-arrow-circle-right::before {
                  content: '\e68d';
                }

                &:hover {
                  background: #dfdfdf !important;
                  color: #191919 !important;
                }

                &:focus {
                  background: #aeaeae !important;
                  color: #191919 !important;
                  outline: 0;
                }


              }
            }

            .right-nav {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              box-shadow: none;
              position: fixed;
              right: 30px;
              z-index: 3;
              top: 700px;
              gap: 16px;


              .nav-item {
                cursor: pointer;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                transition: all 0.1s;
                box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.08);
                background: #fff;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                padding: 12px;
                text-align: center;
                overflow: hidden;
                box-sizing: border-box;

                .iconfont {
                  color: #191919;
                  font-size: 16px;
                }

                .text {
                  display: none;
                  color: #191919;
                  opacity: 0;
                  font-size: 12px;
                  line-height: 20px;
                  white-space: nowrap
                }

                &:hover {
                  width: 72px;
                  height: 56px;
                  border-radius: 12px;
                  background-color: #fff;
                  padding: 8px 12px;

                  .text {
                    opacity: 1;
                    display: block;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}


.rst-content table.docutils:not(.field-list) tr:nth-child(2n-1) td,
.wy-table-backed,
.wy-table-odd td,
.wy-table-striped tr:nth-child(2n-1) td {
  background-color: rgba($color: #dfdfdf, $alpha: 0.5);
}