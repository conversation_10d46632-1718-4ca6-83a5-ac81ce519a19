# 前言<a name="ZH-CN_TOPIC_0000001797563677"></a>

**概述<a name="section669mcpsimp"></a>**

BS2XV100通过API（Application Programming Interface）向开发者提供音频编解码的相关接口，包括编码器初始化及销毁接口、命令配置接口、编码一帧接口、解码一帧接口。

**产品版本<a name="section673mcpsimp"></a>**

与本文档对应的产品版本如下。

<a name="table676mcpsimp"></a>
<table><thead align="left"><tr id="row681mcpsimp"><th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.1"><p id="p683mcpsimp"><a name="p683mcpsimp"></a><a name="p683mcpsimp"></a><strong id="b684mcpsimp"><a name="b684mcpsimp"></a><a name="b684mcpsimp"></a>产品名称</strong></p>
</th>
<th class="cellrowborder" valign="top" width="50%" id="mcps1.1.3.1.2"><p id="p686mcpsimp"><a name="p686mcpsimp"></a><a name="p686mcpsimp"></a><strong id="b687mcpsimp"><a name="b687mcpsimp"></a><a name="b687mcpsimp"></a>产品版本</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row689mcpsimp"><td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.1 "><p id="p691mcpsimp"><a name="p691mcpsimp"></a><a name="p691mcpsimp"></a>BS2X</p>
</td>
<td class="cellrowborder" valign="top" width="50%" headers="mcps1.1.3.1.2 "><p id="p693mcpsimp"><a name="p693mcpsimp"></a><a name="p693mcpsimp"></a>V100</p>
</td>
</tr>
</tbody>
</table>

**读者对象<a name="section694mcpsimp"></a>**

本文档主要适用以下工程师：

-   技术支持工程
-   软件开发工程师

**符号约定<a name="section133020216410"></a>**

在本文中可能出现下列标志，它们所代表的含义如下。

<a name="table2622507016410"></a>
<table><thead align="left"><tr id="row1530720816410"><th class="cellrowborder" valign="top" width="20.580000000000002%" id="mcps1.1.3.1.1"><p id="p6450074116410"><a name="p6450074116410"></a><a name="p6450074116410"></a><strong id="b2136615816410"><a name="b2136615816410"></a><a name="b2136615816410"></a>符号</strong></p>
</th>
<th class="cellrowborder" valign="top" width="79.42%" id="mcps1.1.3.1.2"><p id="p5435366816410"><a name="p5435366816410"></a><a name="p5435366816410"></a><strong id="b5941558116410"><a name="b5941558116410"></a><a name="b5941558116410"></a>说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row1372280416410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p3734547016410"><a name="p3734547016410"></a><a name="p3734547016410"></a><a name="image2670064316410"></a><a name="image2670064316410"></a><span><img class="" id="image2670064316410" src="figures/zh-cn_image_0000001750483984.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p1757432116410"><a name="p1757432116410"></a><a name="p1757432116410"></a>表示如不避免则将会导致死亡或严重伤害的具有高等级风险的危害。</p>
</td>
</tr>
<tr id="row466863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1432579516410"><a name="p1432579516410"></a><a name="p1432579516410"></a><a name="image4895582316410"></a><a name="image4895582316410"></a><span><img class="" id="image4895582316410" src="figures/zh-cn_image_0000001797563685.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p959197916410"><a name="p959197916410"></a><a name="p959197916410"></a>表示如不避免则可能导致死亡或严重伤害的具有中等级风险的危害。</p>
</td>
</tr>
<tr id="row123863216410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p1232579516410"><a name="p1232579516410"></a><a name="p1232579516410"></a><a name="image1235582316410"></a><a name="image1235582316410"></a><span><img class="" id="image1235582316410" src="figures/zh-cn_image_0000001750642900.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p123197916410"><a name="p123197916410"></a><a name="p123197916410"></a>表示如不避免则可能导致轻微或中度伤害的具有低等级风险的危害。</p>
</td>
</tr>
<tr id="row5786682116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p2204984716410"><a name="p2204984716410"></a><a name="p2204984716410"></a><a name="image4504446716410"></a><a name="image4504446716410"></a><span><img class="" id="image4504446716410" src="figures/zh-cn_image_0000001750483980.png" width="55.9265" height="25.270000000000003"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4388861916410"><a name="p4388861916410"></a><a name="p4388861916410"></a>用于传递设备或环境安全警示信息。如不避免则可能会导致设备损坏、数据丢失、设备性能降低或其它不可预知的结果。</p>
<p id="p1238861916410"><a name="p1238861916410"></a><a name="p1238861916410"></a>“须知”不涉及人身伤害。</p>
</td>
</tr>
<tr id="row2856923116410"><td class="cellrowborder" valign="top" width="20.580000000000002%" headers="mcps1.1.3.1.1 "><p id="p5555360116410"><a name="p5555360116410"></a><a name="p5555360116410"></a><a name="image799324016410"></a><a name="image799324016410"></a><span><img class="" id="image799324016410" src="figures/zh-cn_image_0000001797602697.png" width="47.88" height="15.96"></span></p>
</td>
<td class="cellrowborder" valign="top" width="79.42%" headers="mcps1.1.3.1.2 "><p id="p4612588116410"><a name="p4612588116410"></a><a name="p4612588116410"></a>对正文中重点信息的补充说明。</p>
<p id="p1232588116410"><a name="p1232588116410"></a><a name="p1232588116410"></a>“说明”不是安全警示信息，不涉及人身、设备及环境伤害信息。</p>
</td>
</tr>
</tbody>
</table>

**修改记录<a name="section2467512116410"></a>**

<a name="table1557726816410"></a>
<table><thead align="left"><tr id="row2942532716410"><th class="cellrowborder" valign="top" width="19.11%" id="mcps1.1.4.1.1"><p id="p3778275416410"><a name="p3778275416410"></a><a name="p3778275416410"></a><strong id="b5687322716410"><a name="b5687322716410"></a><a name="b5687322716410"></a>文档版本</strong></p>
</th>
<th class="cellrowborder" valign="top" width="25.53%" id="mcps1.1.4.1.2"><p id="p5627845516410"><a name="p5627845516410"></a><a name="p5627845516410"></a><strong id="b5800814916410"><a name="b5800814916410"></a><a name="b5800814916410"></a>发布日期</strong></p>
</th>
<th class="cellrowborder" valign="top" width="55.36%" id="mcps1.1.4.1.3"><p id="p2382284816410"><a name="p2382284816410"></a><a name="p2382284816410"></a><strong id="b3316380216410"><a name="b3316380216410"></a><a name="b3316380216410"></a>修改说明</strong></p>
</th>
</tr>
</thead>
<tbody><tr id="row715820279349"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p590115351346"><a name="p590115351346"></a><a name="p590115351346"></a>03</p>
</td>
<td class="cellrowborder" valign="top" width="25.53%" headers="mcps1.1.4.1.2 "><p id="p1901183523413"><a name="p1901183523413"></a><a name="p1901183523413"></a>2025-05-30</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><a name="ul325332311020"></a><a name="ul325332311020"></a><ul id="ul325332311020"><li>新增“<a href="Audio-Encoder.md">Audio Encoder</a>”的“<a href="开发流程.md">开发流程</a>”小节内容。</li><li>更新“<a href="Audio-Decoder.md">Audio Decoder</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</li></ul>
</td>
</tr>
<tr id="row146004321655"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p1860016321517"><a name="p1860016321517"></a><a name="p1860016321517"></a>02</p>
</td>
<td class="cellrowborder" valign="top" width="25.53%" headers="mcps1.1.4.1.2 "><p id="p116007325513"><a name="p116007325513"></a><a name="p116007325513"></a>2024-07-04</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><a name="ul1289108868"></a><a name="ul1289108868"></a><ul id="ul1289108868"><li>更新“<a href="概述.md">概述</a>”小节内容。</li><li>新增“<a href="Audio-Encoder.md">Audio Encoder</a>”的“<a href="开发流程.md">开发流程</a>”小节内容。</li><li>更新“<a href="Audio-Decoder.md">Audio Decoder</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</li><li>新增“<a href="调试与功能.md">调试与功能</a>”小节内容。</li></ul>
</td>
</tr>
<tr id="row1966813535911"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p0413131712"><a name="p0413131712"></a><a name="p0413131712"></a>01</p>
</td>
<td class="cellrowborder" valign="top" width="25.53%" headers="mcps1.1.4.1.2 "><p id="p184131311111"><a name="p184131311111"></a><a name="p184131311111"></a>2024-05-15</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p169114261115"><a name="p169114261115"></a><a name="p169114261115"></a>第一次正式版本发布。</p>
</td>
</tr>
<tr id="row173391051111720"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p1933905117177"><a name="p1933905117177"></a><a name="p1933905117177"></a>00B02</p>
</td>
<td class="cellrowborder" valign="top" width="25.53%" headers="mcps1.1.4.1.2 "><p id="p533965181715"><a name="p533965181715"></a><a name="p533965181715"></a>2024-01-17</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><a name="ul148421149203014"></a><a name="ul148421149203014"></a><ul id="ul148421149203014"><li>更新“<a href="Audio-Decoder.md">Audio Decoder</a>”的“<a href="开发流程-2.md">开发流程</a>”小节内容。</li><li>新增“<a href="调试与功能.md">调试与功能</a>”章节内容。</li></ul>
</td>
</tr>
<tr id="row5947359616410"><td class="cellrowborder" valign="top" width="19.11%" headers="mcps1.1.4.1.1 "><p id="p2149706016410"><a name="p2149706016410"></a><a name="p2149706016410"></a>00B01</p>
</td>
<td class="cellrowborder" valign="top" width="25.53%" headers="mcps1.1.4.1.2 "><p id="p648803616410"><a name="p648803616410"></a><a name="p648803616410"></a>2024-01-08</p>
</td>
<td class="cellrowborder" valign="top" width="55.36%" headers="mcps1.1.4.1.3 "><p id="p1946537916410"><a name="p1946537916410"></a><a name="p1946537916410"></a>第一次临时版本发布。</p>
</td>
</tr>
</tbody>
</table>

# 概述<a name="ZH-CN_TOPIC_0000001750642884"></a>

BS2XV100通过API（Application Programming Interface）面向开发者提供音频编解码功能的开发和应用接口，包括Audio Encoder、Audio Decoder两个部分。

各组件功能说明如下：

-   Audio Encoder：音频编码器部分，主要提供sbc、msbc、opus、l2hc音频格式的编码功能。
-   Audio Decoder：音频解码器部分，主要解码Audio Encoder编码出的sbc、msbc、opus、l2hc格式的码流。

>![](public_sys-resources/icon-note.gif) **说明：** 
>该文档描述各个模块功能的基本流程和API接口描述。


## 错误码<a name="ZH-CN_TOPIC_0000001750642888"></a>

Audio Codec使用错误码指示用户当前任务执行结果，如下表 错误码所示。

**表 1**  错误码

<a name="table9501182016504"></a>
<table><thead align="left"><tr id="row950292085010"><th class="cellrowborder" valign="top" width="9%" id="mcps1.*******"><p id="p25029205503"><a name="p25029205503"></a><a name="p25029205503"></a>序号</p>
</th>
<th class="cellrowborder" valign="top" width="40.38%" id="mcps1.*******"><p id="p1350272085016"><a name="p1350272085016"></a><a name="p1350272085016"></a>定义</p>
</th>
<th class="cellrowborder" valign="top" width="14.180000000000001%" id="mcps1.*******"><p id="p19502152005012"><a name="p19502152005012"></a><a name="p19502152005012"></a>实际数值</p>
</th>
<th class="cellrowborder" valign="top" width="36.44%" id="mcps1.*******"><p id="p950262016502"><a name="p950262016502"></a><a name="p950262016502"></a>描述</p>
</th>
</tr>
</thead>
<tbody><tr id="row10502132020509"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p750232017508"><a name="p750232017508"></a><a name="p750232017508"></a>1</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p10875192674016"><a name="p10875192674016"></a><a name="p10875192674016"></a>AUDIO_SUCCESS</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1350242035012"><a name="p1350242035012"></a><a name="p1350242035012"></a>0</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p2478152273213"><a name="p2478152273213"></a><a name="p2478152273213"></a>执行成功错误码。</p>
</td>
</tr>
<tr id="row950211209505"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p75021320195017"><a name="p75021320195017"></a><a name="p75021320195017"></a>2</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p153165211412"><a name="p153165211412"></a><a name="p153165211412"></a>ERR_HACODEC_INVALID_PARAM</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p206202225145"><a name="p206202225145"></a><a name="p206202225145"></a>0x80005320</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p2580741135211"><a name="p2580741135211"></a><a name="p2580741135211"></a>非法参数错误码。</p>
</td>
</tr>
<tr id="row647004635216"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p9470164695211"><a name="p9470164695211"></a><a name="p9470164695211"></a>3</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p15376202954111"><a name="p15376202954111"></a><a name="p15376202954111"></a>ERR_HACODEC_INVALID_SAMPLERATE</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1247054675220"><a name="p1247054675220"></a><a name="p1247054675220"></a>0x80005321</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p349816514539"><a name="p349816514539"></a><a name="p349816514539"></a>非法采样率错误码。</p>
</td>
</tr>
<tr id="row208504495415"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p485544155412"><a name="p485544155412"></a><a name="p485544155412"></a>5</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p91211439114117"><a name="p91211439114117"></a><a name="p91211439114117"></a>ERR_HACODEC_INVALID_BITDEPTH</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p202639558212"><a name="p202639558212"></a><a name="p202639558212"></a>0x80005322</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p107082179351"><a name="p107082179351"></a><a name="p107082179351"></a>非法位宽错误码。</p>
</td>
</tr>
<tr id="row2914115711542"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p15914145716545"><a name="p15914145716545"></a><a name="p15914145716545"></a>6</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p17939154313416"><a name="p17939154313416"></a><a name="p17939154313416"></a>ERR_HACODEC_INVALID_CHANNEL</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p061233385012"><a name="p061233385012"></a><a name="p061233385012"></a>0x80005323</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p925873013511"><a name="p925873013511"></a><a name="p925873013511"></a>非法声道数错误码。</p>
</td>
</tr>
<tr id="row13726122553"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p187261621559"><a name="p187261621559"></a><a name="p187261621559"></a>7</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p20928749124111"><a name="p20928749124111"></a><a name="p20928749124111"></a>ERR_HACODEC_NULL_PTR</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1991455717548"><a name="p1991455717548"></a><a name="p1991455717548"></a>0x80005324</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1650784303513"><a name="p1650784303513"></a><a name="p1650784303513"></a>传入空指针错误码。</p>
</td>
</tr>
<tr id="row119961302554"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1799683013557"><a name="p1799683013557"></a><a name="p1799683013557"></a>8</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p141591955104115"><a name="p141591955104115"></a><a name="p141591955104115"></a>ERR_HACODEC_STREAM_CORRUPT</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p10543203162210"><a name="p10543203162210"></a><a name="p10543203162210"></a>0x80005325</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p795215509352"><a name="p795215509352"></a><a name="p795215509352"></a>流损坏错误码。</p>
</td>
</tr>
<tr id="row121607340559"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p4160934185512"><a name="p4160934185512"></a><a name="p4160934185512"></a>9</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p185315094217"><a name="p185315094217"></a><a name="p185315094217"></a>ERR_HACODEC_NOT_ENOUGH_DATA</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p19961530125516"><a name="p19961530125516"></a><a name="p19961530125516"></a>0x80005326</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p03951456183511"><a name="p03951456183511"></a><a name="p03951456183511"></a>输入数据不足错误码。</p>
</td>
</tr>
<tr id="row13618338115517"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3618103810553"><a name="p3618103810553"></a><a name="p3618103810553"></a>10</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p11657560422"><a name="p11657560422"></a><a name="p11657560422"></a>ERR_HACODEC_NOT_SUPPORT</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p196909199227"><a name="p196909199227"></a><a name="p196909199227"></a>0x80005327</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p133310414365"><a name="p133310414365"></a><a name="p133310414365"></a>系统不支持错误码。</p>
</td>
</tr>
<tr id="row179331321145612"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p1793472111566"><a name="p1793472111566"></a><a name="p1793472111566"></a>11</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p5433181194213"><a name="p5433181194213"></a><a name="p5433181194213"></a>ERR_HACODEC_INIT_FAILED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p761819387554"><a name="p761819387554"></a><a name="p761819387554"></a>0x80005328</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1738191073619"><a name="p1738191073619"></a><a name="p1738191073619"></a>初始化失败错误码。</p>
</td>
</tr>
<tr id="row24341235135618"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p3434635155618"><a name="p3434635155618"></a><a name="p3434635155618"></a>12</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p1460411162421"><a name="p1460411162421"></a><a name="p1460411162421"></a>ERR_HACODEC_ENCODE_FAILED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1934132112566"><a name="p1934132112566"></a><a name="p1934132112566"></a>0x80005329</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p154731715123613"><a name="p154731715123613"></a><a name="p154731715123613"></a>编码失败错误码。</p>
</td>
</tr>
<tr id="row625216013410"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p12252200193410"><a name="p12252200193410"></a><a name="p12252200193410"></a>13</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p8980520194217"><a name="p8980520194217"></a><a name="p8980520194217"></a>ERR_HACODEC_DECODE_FAILED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p15434193511569"><a name="p15434193511569"></a><a name="p15434193511569"></a>0x8000532a</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p20720192013616"><a name="p20720192013616"></a><a name="p20720192013616"></a>解码失败错误码。</p>
</td>
</tr>
<tr id="row22071149346"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p132076412341"><a name="p132076412341"></a><a name="p132076412341"></a>14</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p20663172612421"><a name="p20663172612421"></a><a name="p20663172612421"></a>ERR_HACODEC_WRONG_HEADER</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1425210017341"><a name="p1425210017341"></a><a name="p1425210017341"></a>0x8000532b</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p14652182523613"><a name="p14652182523613"></a><a name="p14652182523613"></a>帧头错误错误码。</p>
</td>
</tr>
<tr id="row19824101991719"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p15824111911170"><a name="p15824111911170"></a><a name="p15824111911170"></a>15</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p0440164284212"><a name="p0440164284212"></a><a name="p0440164284212"></a>ERR_HACODEC_ALLOC_MEM_FAILED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p182072044349"><a name="p182072044349"></a><a name="p182072044349"></a>0x8000532c</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p24881812275"><a name="p24881812275"></a><a name="p24881812275"></a>内存申请失败错误码。</p>
</td>
</tr>
<tr id="row1812615238178"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p171261623181716"><a name="p171261623181716"></a><a name="p171261623181716"></a>16</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p12798144814425"><a name="p12798144814425"></a><a name="p12798144814425"></a>ERR_HACODEC_MEM_COPY_FAILED</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p48241119151715"><a name="p48241119151715"></a><a name="p48241119151715"></a>0x8000532d</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p4126132321714"><a name="p4126132321714"></a><a name="p4126132321714"></a>内存拷贝失败错误码。</p>
</td>
</tr>
<tr id="row128112720177"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p18811527151714"><a name="p18811527151714"></a><a name="p18811527151714"></a>17</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p5137125510427"><a name="p5137125510427"></a><a name="p5137125510427"></a>ERR_HACODEC_NOT_ENOUGH_OUT_BUFFER_SIZE</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p1812620235173"><a name="p1812620235173"></a><a name="p1812620235173"></a>0x8000532e</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p4811270173"><a name="p4811270173"></a><a name="p4811270173"></a>输出buffer空间不足错误码。</p>
</td>
</tr>
<tr id="row1674155911712"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p4674059151713"><a name="p4674059151713"></a><a name="p4674059151713"></a>18</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p18416190104315"><a name="p18416190104315"></a><a name="p18416190104315"></a>ERR_HACODEC_NOT_ENOUGH_SCRATCH_BUFFER_SIZE</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p78116279173"><a name="p78116279173"></a><a name="p78116279173"></a>0x8000532f</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p1674135911170"><a name="p1674135911170"></a><a name="p1674135911170"></a>暂存缓冲区空间不足错误码。</p>
</td>
</tr>
<tr id="row18549125313551"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p13631719154219"><a name="p13631719154219"></a><a name="p13631719154219"></a>19</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p176311914429"><a name="p176311914429"></a><a name="p176311914429"></a>ERR_HACODEC_READ_FIFO_NOT_ENOUGH_DATA</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p7631919194212"><a name="p7631919194212"></a><a name="p7631919194212"></a>0x80005330</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p18631319104219"><a name="p18631319104219"></a><a name="p18631319104219"></a>FIFO中无足够数据错误码</p>
</td>
</tr>
<tr id="row9784759175519"><td class="cellrowborder" valign="top" width="9%" headers="mcps1.******* "><p id="p2530131124211"><a name="p2530131124211"></a><a name="p2530131124211"></a>20</p>
</td>
<td class="cellrowborder" valign="top" width="40.38%" headers="mcps1.******* "><p id="p65301312426"><a name="p65301312426"></a><a name="p65301312426"></a>ERR_HACODEC_WRITE_FIFO_OVERFLOW</p>
</td>
<td class="cellrowborder" valign="top" width="14.180000000000001%" headers="mcps1.******* "><p id="p9530163116428"><a name="p9530163116428"></a><a name="p9530163116428"></a>0x80005331</p>
</td>
<td class="cellrowborder" valign="top" width="36.44%" headers="mcps1.******* "><p id="p155301831164217"><a name="p155301831164217"></a><a name="p155301831164217"></a>写FIFO溢出错误码</p>
</td>
</tr>
</tbody>
</table>

# Audio Encoder<a name="ZH-CN_TOPIC_0000001797563673"></a>




## 概述<a name="ZH-CN_TOPIC_0000001797602677"></a>

Audio Encoder主要提供音频编码功能，支持的规格有：

-   MSBC：16k
-   SBC：16k、48k
-   opus：16k、48k
-   l2hc：16k、48k

数据由MIC（AMIC/DMIC）采集，由PDM模块处理后送至Audio模块，由Audio Encoder进行编码。

## 开发流程<a name="ZH-CN_TOPIC_0000001797602681"></a>

**使用场景<a name="section775mcpsimp"></a>**

当设备为发送端，需要音频功能时，由Audio Encoder提供音频编码功能。

**参数及数据结构<a name="section1317564152619"></a>**

本章节主要介绍编码涉及数据结构。

1.open param 公共参数，[表1](#_table213321716161)中初始化用作传参的结构体。

```
ha_codec_enc_param {
    bool interleaved； /* 解码解交织模式 */
    uint32_t channels; /* 声道数 */
    int32_t bit_depth; /* 位宽 */
    uint32_t sample_rate; /* 采样率 */
    uint32_t samples_per_frame; /* 每帧采样点 */
    void *private_data； /* 编码器私有参数 */
    uint32_t private_data_size; /* 解码器私有参数大小 */
}
```

2.in\_pkt编码器输入buf，[表1](#_table213321716161)中作为编码输入的结构体。

```
ha_codec_enc_in_packet {
    int64_tpts; /* 音频同步使用，保留参数，暂不使用 */
    uint8_t *data; /* 输入数据指针 */
    uint32_t size; /* 输入数据大小 */
}
```

3.out\_pkt编码器输出buf，[表1](#_table213321716161)中作为编码输出的结构体。

```
ha_codec_enc_out_packet {
    int32_t *bits_out_buf; /* 编码器输出buffer */
    uint32_t bits_out_buf_size; /* 编码器输出buffer大小*/
    uint32_t bits_out_bytes; /* 编码数据帧大小，单位Byte */
    uint32_t bit_rate; /* 比特率 */
}
```

4.send\_cmd命令字段，[表1](#_table213321716161)中cmd命令字。

ACODEC\_PRIVATE\_PARAM\_GET\_CMD  0xFFFF100C 获取编解码器私有参数

ACODEC\_IN\_FRAME\_SIZE\_GET\_CMD  0xFFFF100D 获取编码输入数据大小

ACODEC\_OUT\_FRAME\_SIZE\_GET\_CMD  0xFFFF100E 获取编码输出数据大小

ACODEC\_SET\_PLC\_OFF\_THR\_CMD  0xFFFF1010 设置plc最大帧数，仅sbc解码有

ACODEC\_GET\_PLC\_OFF\_THR\_CMD  0xFFFF1011  获取当前plc最大帧数，仅sbc解码有

**功能<a name="section779mcpsimp"></a>**

Audio Encoder提供的接口如[表1](#_table213321716161)所示。

**表 1**  Audio Encoder接口

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="22.63%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="17.43%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="31.330000000000002%" id="mcps1.*******"><p id="p142843230222"><a name="p142843230222"></a><a name="p142843230222"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="28.610000000000003%" id="mcps1.*******"><p id="p79178221244"><a name="p79178221244"></a><a name="p79178221244"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row109671129123514"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1996742933517"><a name="p1996742933517"></a><a name="p1996742933517"></a>name</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p189672029143520"><a name="p189672029143520"></a><a name="p189672029143520"></a>获取编码器名称</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p15952455154812"><a name="p15952455154812"></a><a name="p15952455154812"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p11967152913520"><a name="p11967152913520"></a><a name="p11967152913520"></a>char</p>
</td>
</tr>
<tr id="row93910277358"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p113982753510"><a name="p113982753510"></a><a name="p113982753510"></a>codec_id</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p4391727103520"><a name="p4391727103520"></a><a name="p4391727103520"></a>获取编码器id</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p2954255164820"><a name="p2954255164820"></a><a name="p2954255164820"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p5802134813719"><a name="p5802134813719"></a><a name="p5802134813719"></a>enum</p>
</td>
</tr>
<tr id="row14862523143519"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p686222316354"><a name="p686222316354"></a><a name="p686222316354"></a>version.version</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p138623239351"><a name="p138623239351"></a><a name="p138623239351"></a>获取版本号</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p1955185594815"><a name="p1955185594815"></a><a name="p1955185594815"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p1286272373511"><a name="p1286272373511"></a><a name="p1286272373511"></a>int</p>
</td>
</tr>
<tr id="row18901646113418"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p188908469347"><a name="p188908469347"></a><a name="p188908469347"></a>description</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1589004673414"><a name="p1589004673414"></a><a name="p1589004673414"></a>获取描述</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p149565558481"><a name="p149565558481"></a><a name="p149565558481"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p1949512448387"><a name="p1949512448387"></a><a name="p1949512448387"></a>char</p>
</td>
</tr>
<tr id="row154823161515"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1724715716352"><a name="p1724715716352"></a><a name="p1724715716352"></a>init</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p15485312159"><a name="p15485312159"></a><a name="p15485312159"></a>编码器初始化</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><a name="ul6611175920481"></a><a name="ul6611175920481"></a><ul id="ul6611175920481"><li>encoder：二维指针，指向编码器实例的地址</li><li>open_param：公共参数，如编码器的采样率、位宽、声道数等</li></ul>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p998181701718"><a name="p998181701718"></a><a name="p998181701718"></a>错误码。</p>
</td>
</tr>
<tr id="row43102035171512"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p2352131733519"><a name="p2352131733519"></a><a name="p2352131733519"></a>deinit</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p183118354157"><a name="p183118354157"></a><a name="p183118354157"></a>编码器去初始化，释放编码使用的内存空间</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p0284723122215"><a name="p0284723122215"></a><a name="p0284723122215"></a>encoder：编码器实例地址</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p134171515151711"><a name="p134171515151711"></a><a name="p134171515151711"></a>错误码。</p>
</td>
</tr>
<tr id="row106094013183"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p77391225163517"><a name="p77391225163517"></a><a name="p77391225163517"></a>send_cmd</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1860740181818"><a name="p1860740181818"></a><a name="p1860740181818"></a>cmd接口，可获取输入输出所需数据大小</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><a name="ul064712209497"></a><a name="ul064712209497"></a><ul id="ul064712209497"><li>encoder：编码器实例地址</li><li>cmd：cmd命令字</li><li>arg：接收返回值</li></ul>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p3320171314172"><a name="p3320171314172"></a><a name="p3320171314172"></a>错误码。</p>
</td>
</tr>
<tr id="row804mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p188373342352"><a name="p188373342352"></a><a name="p188373342352"></a>get_max_bits_out_size</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p808mcpsimp"><a name="p808mcpsimp"></a><a name="p808mcpsimp"></a>获取最大输出大小</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p3890283569"><a name="p3890283569"></a><a name="p3890283569"></a>encoder：编码器实例地址</p>
<p id="p91661323247"><a name="p91661323247"></a><a name="p91661323247"></a>out_sizes：接收输出大小size</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p5422117171"><a name="p5422117171"></a><a name="p5422117171"></a>错误码。</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p1883734519512"><a name="p1883734519512"></a><a name="p1883734519512"></a>encode_frame</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>编码接口</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><a name="ul18989172619490"></a><a name="ul18989172619490"></a><ul id="ul18989172619490"><li>encoder：编码器实例地址</li><li>in_pkt：输入数据（编码前数据）buffer</li><li>out_pkt：输出数据（编码后数据）buffer</li></ul>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p11816872176"><a name="p11816872176"></a><a name="p11816872176"></a>错误码。</p>
</td>
</tr>
<tr id="row899921411325"><td class="cellrowborder" valign="top" width="22.63%" headers="mcps1.******* "><p id="p129998148329"><a name="p129998148329"></a><a name="p129998148329"></a>overlay_enc_entry</p>
</td>
<td class="cellrowborder" valign="top" width="17.43%" headers="mcps1.******* "><p id="p1399931418321"><a name="p1399931418321"></a><a name="p1399931418321"></a>封装上述编码器接</p>
</td>
<td class="cellrowborder" valign="top" width="31.330000000000002%" headers="mcps1.******* "><p id="p996710291358"><a name="p996710291358"></a><a name="p996710291358"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="28.610000000000003%" headers="mcps1.******* "><p id="p17999414133211"><a name="p17999414133211"></a><a name="p17999414133211"></a>NULL</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

BS2X支持多规格编码器，编码器调用接口抽象为overlay\_enc\_entry，接口返回值解引用后对应元素为编码器实例，实例如[表1](#_table213321716161)所示。

Audio Encoder典型开发流程如下。

**Server Node：**

1.  调用overlay\_enc\_entry\(\)-\>init（参数如[表1](#_table213321716161)所示），完成编码器初始化。
2.  调用overlay\_enc\_entry\(\)-\>get\_max\_bits\_out\_size（参数如[表1](#_table213321716161)所示），获取编码器所需的输出buffer大小，用于判断当前系统中的预留空间是否满足需求。
3.  调用overlay\_enc\_entry\(\)-\>encode\_frame参数（参数如[表1](#_table213321716161)所示），对输入的数据进行编码，可通过返回值判断是否编码成功，如果返回AUDIO\_SUCCESS，则表明编码成功，in\_pkt参数的data指针会偏移掉一帧pcm的数据量，in\_pkt参数的size大小会减去编码消耗的pcm数据量的大小。编码后输出码流写入out\_pkt参数的bits\_out\_buf指针指向的空间，out\_pkt参数的bits\_out\_bytes变量刷新为编码输出码率的数据量。如果编码接口返回值不为AUDIO\_SUCCESS，则可根据“”中的错误码确定失败类型。编码失败，in\_pkt中的数据不会消耗。
4.  连续调用overlay\_enc\_entry\(\)-\>encode\_frame，直至输入buffer数据全部处理，外部不再送数据，编码完成。
5.  调用overlay\_enc\_entry\(\)-\>deinit（参数如[表1](#_table213321716161)所示），完成编码器销毁。

**示例伪代码<a name="section128586715157"></a>**

```
extern void *overlay_enc_entry(void);
int main()
{
    设置open_param参数
    overlay_enc_entry()->init/* 初始化 */

    overlay_enc_entry()->get_max_bits_out_size /* 获取所需输出buff大小 */
    if（buff大小不满足）
    overlay_enc_entry()->deinit；/* 销毁编码器 */

    while （编码未完成）
    overlay_enc_entry()->encode_frame；
    if（编码成功）
    根据out_buf更新输入数据指针及size；
    else
    根据错误码处理；
    end if

    if （编码完成）
    break；

    overlay_enc_entry()->deinit /* 编码完成，销毁编码器 */
}
```

## 注意事项<a name="ZH-CN_TOPIC_0000001797602685"></a>

单片仅支持一种规格的编码器，如：若支持sbc编码就不支持其他两种格式的编码。

# Audio Decoder<a name="ZH-CN_TOPIC_0000001797563669"></a>




## 概述<a name="ZH-CN_TOPIC_0000001750483964"></a>

Audio Decoder主要提供音频解码功能，支持的规格有：

-   MSBC：16k
-   SBC：16k、48k
-   opus：16k、48k
-   l2hc：16k、48k

## 开发流程<a name="ZH-CN_TOPIC_0000001750483968"></a>

**使用场景<a name="section775mcpsimp"></a>**

当设备为接收端，需要音频功能时，由Audio Decoder提供音频解码功能。

**参数及数据结构<a name="section1317564152619"></a>**

本章节主要介绍编码涉及数据结构。

1.open param 公共参数，[表1](#_table213321716161)中初始化用作传参的结构体。

```
ha_codec_dec_param {
    uint32_t channels; /* 声道数 */
    int32_t bit_depth; /* 位宽 */
    uint32_t sample_rate; /* 采样率 */
    void *private_data； /* 编码器私有参数 */
    uint32_t private_data_size; /* 解码器私有参数大小 */
}
```

2.in\_pkt编码器输入buf，[表1](#_table213321716161)中作为解码输入的结构体。

```
ha_codec_dec_in_packet {
    int64_t pts; /* 音频同步使用 */
    uint8_t *data; /* 输入数据指针，解码完成后更新该指针 */
    uint32_t size; /* 输入数据大小 */
    bool eos; /* 是否为最后一帧 */
    bool pag_loss; /* 是否丢包 */
}
```

3.out\_pkt编码器输出buf，[表1](#_table213321716161)中作为解码输出的结构体。

```
ha_codec_dec_out_packet {
    int32_t *pcm_out_buf; /* 解码器输出buffer */
    uint32_t pcm_out_buf_size; /* 解码器输出buffer大小*/
    int32_t *bits_out_buf; /* 指向透传解码输出指针 */
    uint32_t bits_out_buf_size; /* 透传缓冲区大小 */
    uint32_t pcm_out_samples; /* 解码pcm输出采样点 */
    bool interleaved； /* 解码解交织模式 */
    uint32_t bit_per_sample; /* 位宽 */
    uint32_t bits_out_bytes; /* 透传输出帧大小 */
    uint32_t out_channels; /* 输出声道数 */
    uint32_t out_sample_rate; /* 输出采样率 */
    uint32_t org_channels; /* 码流原始声道数 */
    uint32_t org_sample_rate; /* 原始码流采样率 */
    uint32_t bit_rate; /* 码流压缩率 */
    ha_codec_out_pts_info; /* pts信息 */
    uint32_t frame_index; /* 输出帧序号 */
}
```

4.send\_cmd命令字段，[表1](#_table213321716161)中cmd命令字

ACODEC\_PRIVATE\_PARAM\_GET\_CMD 0xFFFF100C 获取解码器私有参数

ACODEC\_IN\_FRAME\_SIZE\_GET\_CMD  0xFFFF100D 获取解码输入数据大小

ACODEC\_OUT\_FRAME\_SIZE\_GET\_CMD  0xFFFF100E 获取解码输出数据大小

**功能<a name="section779mcpsimp"></a>**

Audio Decoder提供的接口如[表1](#_table213321716161)所示。

**表 1**  Audio Decoder接口

<a name="_table213321716161"></a>
<table><thead align="left"><tr id="row788mcpsimp"><th class="cellrowborder" valign="top" width="17.931793179317932%" id="mcps1.*******"><p id="p790mcpsimp"><a name="p790mcpsimp"></a><a name="p790mcpsimp"></a>接口名称</p>
</th>
<th class="cellrowborder" valign="top" width="31.14311431143114%" id="mcps1.*******"><p id="p792mcpsimp"><a name="p792mcpsimp"></a><a name="p792mcpsimp"></a>描述</p>
</th>
<th class="cellrowborder" valign="top" width="27.562756275627564%" id="mcps1.*******"><p id="p142843230222"><a name="p142843230222"></a><a name="p142843230222"></a>参数说明</p>
</th>
<th class="cellrowborder" valign="top" width="23.362336233623363%" id="mcps1.*******"><p id="p79178221244"><a name="p79178221244"></a><a name="p79178221244"></a>返回信息说明</p>
</th>
</tr>
</thead>
<tbody><tr id="row8145203391"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p1996742933517"><a name="p1996742933517"></a><a name="p1996742933517"></a>name</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p189672029143520"><a name="p189672029143520"></a><a name="p189672029143520"></a>获取解码器名称</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p996710291358"><a name="p996710291358"></a><a name="p996710291358"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p11967152913520"><a name="p11967152913520"></a><a name="p11967152913520"></a>char</p>
</td>
</tr>
<tr id="row1124458123818"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p113982753510"><a name="p113982753510"></a><a name="p113982753510"></a>codec_id</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p4391727103520"><a name="p4391727103520"></a><a name="p4391727103520"></a>获取解码器id</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p7665141410488"><a name="p7665141410488"></a><a name="p7665141410488"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p5802134813719"><a name="p5802134813719"></a><a name="p5802134813719"></a>enum</p>
</td>
</tr>
<tr id="row717255616384"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p686222316354"><a name="p686222316354"></a><a name="p686222316354"></a>version.version</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p138623239351"><a name="p138623239351"></a><a name="p138623239351"></a>获取版本号</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p46701814124814"><a name="p46701814124814"></a><a name="p46701814124814"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1286272373511"><a name="p1286272373511"></a><a name="p1286272373511"></a>int</p>
</td>
</tr>
<tr id="row625145433813"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p188908469347"><a name="p188908469347"></a><a name="p188908469347"></a>description</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p1589004673414"><a name="p1589004673414"></a><a name="p1589004673414"></a>获取描述</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p1167151494815"><a name="p1167151494815"></a><a name="p1167151494815"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p1949512448387"><a name="p1949512448387"></a><a name="p1949512448387"></a>char</p>
</td>
</tr>
<tr id="row794mcpsimp"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p1724715716352"><a name="p1724715716352"></a><a name="p1724715716352"></a>init</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p15485312159"><a name="p15485312159"></a><a name="p15485312159"></a>解码器初始化</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><a name="ul15754184612474"></a><a name="ul15754184612474"></a><ul id="ul15754184612474"><li>decoder：二维指针，指向解码器实例的地址</li><li>open_param：公共参数，如解码器的采样率、位宽、声道数等</li></ul>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p998181701718"><a name="p998181701718"></a><a name="p998181701718"></a>错误码</p>
</td>
</tr>
<tr id="row799mcpsimp"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p2352131733519"><a name="p2352131733519"></a><a name="p2352131733519"></a>deinit</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p183118354157"><a name="p183118354157"></a><a name="p183118354157"></a>解码器去初始化，释放解码使用的内存空间</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p0284723122215"><a name="p0284723122215"></a><a name="p0284723122215"></a>decoder：解码器实例地址</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p134171515151711"><a name="p134171515151711"></a><a name="p134171515151711"></a>错误码</p>
</td>
</tr>
<tr id="row809mcpsimp"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p77391225163517"><a name="p77391225163517"></a><a name="p77391225163517"></a>send_cmd</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p1860740181818"><a name="p1860740181818"></a><a name="p1860740181818"></a>cmd接口，可获取输入输出所需数据大小</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><a name="ul16396124412476"></a><a name="ul16396124412476"></a><ul id="ul16396124412476"><li>decoder：解码器实例地址</li><li>cmd：cmd命令字</li><li>arg：接收返回值</li></ul>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p3320171314172"><a name="p3320171314172"></a><a name="p3320171314172"></a>错误码</p>
</td>
</tr>
<tr id="row14457165322015"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p188373342352"><a name="p188373342352"></a><a name="p188373342352"></a>get_max_pcm_out_size</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p808mcpsimp"><a name="p808mcpsimp"></a><a name="p808mcpsimp"></a>获取最大输出大小</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><a name="ul156021950204717"></a><a name="ul156021950204717"></a><ul id="ul156021950204717"><li>decoder：解码器实例地址</li><li>out_sizes：接收输出大小size</li></ul>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p5422117171"><a name="p5422117171"></a><a name="p5422117171"></a>错误码</p>
</td>
</tr>
<tr id="row824mcpsimp"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p1883734519512"><a name="p1883734519512"></a><a name="p1883734519512"></a>decode_frame</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p813mcpsimp"><a name="p813mcpsimp"></a><a name="p813mcpsimp"></a>解码接口</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><a name="ul1981918481477"></a><a name="ul1981918481477"></a><ul id="ul1981918481477"><li>decoder：解码器实例地址</li><li>in_pkt：输入数据（解码前数据）buffer</li><li>out_pkt：输出数据（解码后数据）buffer</li></ul>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p11816872176"><a name="p11816872176"></a><a name="p11816872176"></a>错误码</p>
</td>
</tr>
<tr id="row1257712474513"><td class="cellrowborder" valign="top" width="17.931793179317932%" headers="mcps1.******* "><p id="p129998148329"><a name="p129998148329"></a><a name="p129998148329"></a>overlay_dec_entry</p>
</td>
<td class="cellrowborder" valign="top" width="31.14311431143114%" headers="mcps1.******* "><p id="p1399931418321"><a name="p1399931418321"></a><a name="p1399931418321"></a>封装上述编码器接口</p>
</td>
<td class="cellrowborder" valign="top" width="27.562756275627564%" headers="mcps1.******* "><p id="p145611165484"><a name="p145611165484"></a><a name="p145611165484"></a>-</p>
</td>
<td class="cellrowborder" valign="top" width="23.362336233623363%" headers="mcps1.******* "><p id="p19563121624812"><a name="p19563121624812"></a><a name="p19563121624812"></a>-</p>
</td>
</tr>
</tbody>
</table>

**开发流程<a name="section904mcpsimp"></a>**

BS2X支持多规格解码器，解码器调用接口抽象为overlay\_dec\_entry，接口返回值解引用后对应元素为解码器实例，实例如[表1](#_table213321716161)所示。

Audio Decoder典型的开发流程如下。

**Server Node：**

1.  调用overlay\_dec\_entry\(\)-\>init（参数如[表1](#_table213321716161)所示），完成解码器初始化；
2.  调用overlay\_dec\_entry\(\)-\>get\_max\_pcm\_out\_size（参数如[表1](#_table213321716161)所示），获取解码器所需的输出buffer大小，用于判断当前系统中的预留空间是否满足需求；
3.  调用overlay\_dec\_entry\(\)-\>decode\_frame（参数如[表1](#_table213321716161)所示），对输入的数据进行解码，可通过返回值判断是否解码成功，如果返回AUDIO\_SUCCESS，则表明解码成功，in\_pkt参数的size指针会减少消耗的数据量的大小，例如原始值为1024，消耗68Byte数据，则size的值减少68，in\_pkt参数的data指针偏移68Byte。解码生成的pcm数据写入out\_pkt参数的pcm\_out\_buf指针指向的空间，pcm\_out\_buf\_size变量刷新为输出pcm数据的大小。如果编码接口返回值不为AUDIO\_SUCCESS，则可根据[错误码](错误码.md)中的错误码确定失败类型。解码失败，in\_pkt中的数据也可能会消耗，可根据in\_pkt参数中的size变量值的变化情况判断输入数据的消耗情况；
4.  连续调用overlay\_dec\_entry\(\)-\>decode\_frame，直至输入buffer数据全部处理，外部不再送数据，解码完成；
5.  调用overlay\_dec\_entry\(\)-\>deinit（参数如[表1](#_table213321716161)所示），完成解码器销毁

**示例伪代码<a name="section128586715157"></a>**

```
extern void *overlay_dec_entry(void);
int main()
{
    设置open_param参数
    overlay_dec_entry()->init/* 初始化 */

    overlay_dec_entry()->get_max_pcm_out_size /* 获取所需输出buff大小 */
    if（buff大小不满足）
    overlay_dec_entry()->deinit；/* 销毁解码器 */

    while （解码未完成）
    overlay_dec_entry()->decode_frame；
    if（编码成功）
    根据out_buf更新输入数据指针及size；
    else
    根据错误码处理；
    end if

    if （解码完成）
    break；

    overlay_dec_entry()->deinit /* 解码完成，销毁解码器 */
}
```

## 注意事项<a name="ZH-CN_TOPIC_0000001802922137"></a>

-   单片仅支持一种规格的解码器，如：若支持sbc解码就不支持其他两种格式的解码。
-   解码器需要与本产品编码器配对使用。

# 调试与功能<a name="ZH-CN_TOPIC_0000001778115342"></a>

**ITCM链接<a name="section1819805164014"></a>**

链接脚本路径：drivers\\chips\\bs2x\\board\\linker\\standard\\linker.prelds

通过.plt\_ramtext段将代码链接至ITCM上。

如链接libsbc\_enc\_all\_itcm.a，在段中添加\*libsbc\_enc\_all\_itcm.a:\*.o\* \(.text .text\* .rodata .rodata\* .srodata .srodata\*\)。

**性能数据表<a name="section148422874812"></a>**

<a name="table1794181495016"></a>
<table><thead align="left"><tr id="row949171585012"><th class="cellrowborder" valign="top" width="10.58105810581058%" id="mcps1.*******"><p id="p13491415125013"><a name="p13491415125013"></a><a name="p13491415125013"></a>CODEC</p>
</th>
<th class="cellrowborder" valign="top" width="8.870887088708871%" id="mcps1.*******"><p id="p14491515145010"><a name="p14491515145010"></a><a name="p14491515145010"></a>类型</p>
</th>
<th class="cellrowborder" valign="top" width="10.47104710471047%" id="mcps1.*******"><p id="p111359214359"><a name="p111359214359"></a><a name="p111359214359"></a>帧长</p>
</th>
<th class="cellrowborder" valign="top" width="10.901090109010902%" id="mcps1.1.9.1.4"><p id="p103446426351"><a name="p103446426351"></a><a name="p103446426351"></a>采样率</p>
</th>
<th class="cellrowborder" valign="top" width="12.5012501250125%" id="mcps1.1.9.1.5"><p id="p201951351133516"><a name="p201951351133516"></a><a name="p201951351133516"></a>算力</p>
</th>
<th class="cellrowborder" valign="top" width="12.5012501250125%" id="mcps1.1.9.1.6"><p id="p25021535012"><a name="p25021535012"></a><a name="p25021535012"></a>ITCM</p>
</th>
<th class="cellrowborder" valign="top" width="17.18171817181718%" id="mcps1.1.9.1.7"><p id="p05010153504"><a name="p05010153504"></a><a name="p05010153504"></a>动态空间</p>
</th>
<th class="cellrowborder" valign="top" width="16.99169916991699%" id="mcps1.1.9.1.8"><p id="p3196201353517"><a name="p3196201353517"></a><a name="p3196201353517"></a>码率</p>
</th>
</tr>
</thead>
<tbody><tr id="row85410157508"><td class="cellrowborder" rowspan="8" valign="top" width="10.58105810581058%" headers="mcps1.******* "><p id="p354315155019"><a name="p354315155019"></a><a name="p354315155019"></a>sbc</p>
</td>
<td class="cellrowborder" rowspan="4" valign="top" width="8.870887088708871%" headers="mcps1.******* "><p id="p135419152505"><a name="p135419152505"></a><a name="p135419152505"></a>编码</p>
</td>
<td class="cellrowborder" valign="top" width="10.47104710471047%" headers="mcps1.******* "><p id="p954171518507"><a name="p954171518507"></a><a name="p954171518507"></a>8ms</p>
</td>
<td class="cellrowborder" valign="top" width="10.901090109010902%" headers="mcps1.1.9.1.4 "><p id="p14541715185016"><a name="p14541715185016"></a><a name="p14541715185016"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.5 "><p id="p10541115185012"><a name="p10541115185012"></a><a name="p10541115185012"></a>5Mcps</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.6 "><p id="p15451515502"><a name="p15451515502"></a><a name="p15451515502"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="17.18171817181718%" headers="mcps1.1.9.1.7 "><p id="p12541515195012"><a name="p12541515195012"></a><a name="p12541515195012"></a>1.93kByte</p>
</td>
<td class="cellrowborder" valign="top" width="16.99169916991699%" headers="mcps1.1.9.1.8 "><p id="p45451510502"><a name="p45451510502"></a><a name="p45451510502"></a>68kbps</p>
</td>
</tr>
<tr id="row854141525014"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1054315135017"><a name="p1054315135017"></a><a name="p1054315135017"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p75415159505"><a name="p75415159505"></a><a name="p75415159505"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p10541715155013"><a name="p10541715155013"></a><a name="p10541715155013"></a>10.2Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p175431511505"><a name="p175431511505"></a><a name="p175431511505"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p105481512509"><a name="p105481512509"></a><a name="p105481512509"></a>1.93k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p18541215135017"><a name="p18541215135017"></a><a name="p18541215135017"></a>126kbps</p>
</td>
</tr>
<tr id="row554415145012"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p17541415175011"><a name="p17541415175011"></a><a name="p17541415175011"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p054101519509"><a name="p054101519509"></a><a name="p054101519509"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1854161519507"><a name="p1854161519507"></a><a name="p1854161519507"></a>10.5Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p1254181545016"><a name="p1254181545016"></a><a name="p1254181545016"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p175411154507"><a name="p175411154507"></a><a name="p175411154507"></a>1.93k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p13541315145016"><a name="p13541315145016"></a><a name="p13541315145016"></a>204kbps</p>
</td>
</tr>
<tr id="row19542152506"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p105431525019"><a name="p105431525019"></a><a name="p105431525019"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p165412156502"><a name="p165412156502"></a><a name="p165412156502"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p135418158504"><a name="p135418158504"></a><a name="p135418158504"></a>10.8Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p055215145014"><a name="p055215145014"></a><a name="p055215145014"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p45511514509"><a name="p45511514509"></a><a name="p45511514509"></a>1.93k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p13552015165018"><a name="p13552015165018"></a><a name="p13552015165018"></a>258kbps</p>
</td>
</tr>
<tr id="row175521510502"><td class="cellrowborder" rowspan="4" valign="top" headers="mcps1.******* "><p id="p11551815145019"><a name="p11551815145019"></a><a name="p11551815145019"></a>解码</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p855161513503"><a name="p855161513503"></a><a name="p855161513503"></a>8ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p155415195018"><a name="p155415195018"></a><a name="p155415195018"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p175521555013"><a name="p175521555013"></a><a name="p175521555013"></a>4Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p8551015145015"><a name="p8551015145015"></a><a name="p8551015145015"></a>13k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p1155815185014"><a name="p1155815185014"></a><a name="p1155815185014"></a>1.14k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.7 "><p id="p9551715125016"><a name="p9551715125016"></a><a name="p9551715125016"></a>68kbps</p>
</td>
</tr>
<tr id="row5551515145011"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p255101595016"><a name="p255101595016"></a><a name="p255101595016"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p8557159504"><a name="p8557159504"></a><a name="p8557159504"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p20552154508"><a name="p20552154508"></a><a name="p20552154508"></a>8.5Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p16555158503"><a name="p16555158503"></a><a name="p16555158503"></a>13k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p289123233610"><a name="p289123233610"></a><a name="p289123233610"></a>1.14k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p10553157501"><a name="p10553157501"></a><a name="p10553157501"></a>126kbps</p>
</td>
</tr>
<tr id="row125517158502"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p135571513505"><a name="p135571513505"></a><a name="p135571513505"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p205521595014"><a name="p205521595014"></a><a name="p205521595014"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p855121519503"><a name="p855121519503"></a><a name="p855121519503"></a>8.7Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p125531519505"><a name="p125531519505"></a><a name="p125531519505"></a>13k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p8893113211367"><a name="p8893113211367"></a><a name="p8893113211367"></a>1.14k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p125520155500"><a name="p125520155500"></a><a name="p125520155500"></a>204kbps</p>
</td>
</tr>
<tr id="row255015185017"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p85541513502"><a name="p85541513502"></a><a name="p85541513502"></a>2.67ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p955715195015"><a name="p955715195015"></a><a name="p955715195015"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p125531513506"><a name="p125531513506"></a><a name="p125531513506"></a>8.9Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p12555151501"><a name="p12555151501"></a><a name="p12555151501"></a>13k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p168945326363"><a name="p168945326363"></a><a name="p168945326363"></a>1.14k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p1456161510503"><a name="p1456161510503"></a><a name="p1456161510503"></a>258kbps</p>
</td>
</tr>
<tr id="row85671518500"><td class="cellrowborder" rowspan="2" valign="top" width="10.58105810581058%" headers="mcps1.******* "><p id="p1256191535014"><a name="p1256191535014"></a><a name="p1256191535014"></a>msbc</p>
</td>
<td class="cellrowborder" valign="top" width="8.870887088708871%" headers="mcps1.******* "><p id="p1456161518506"><a name="p1456161518506"></a><a name="p1456161518506"></a>编码</p>
</td>
<td class="cellrowborder" valign="top" width="10.47104710471047%" headers="mcps1.******* "><p id="p195610159503"><a name="p195610159503"></a><a name="p195610159503"></a>7.5ms</p>
</td>
<td class="cellrowborder" valign="top" width="10.901090109010902%" headers="mcps1.1.9.1.4 "><p id="p056315145018"><a name="p056315145018"></a><a name="p056315145018"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.5 "><p id="p195661555012"><a name="p195661555012"></a><a name="p195661555012"></a>5Mcps</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.6 "><p id="p1156181545016"><a name="p1156181545016"></a><a name="p1156181545016"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="17.18171817181718%" headers="mcps1.1.9.1.7 "><p id="p205601511502"><a name="p205601511502"></a><a name="p205601511502"></a>1.93k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="16.99169916991699%" headers="mcps1.1.9.1.8 "><p id="p75651511504"><a name="p75651511504"></a><a name="p75651511504"></a>64kbps</p>
</td>
</tr>
<tr id="row10561153509"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1556131520505"><a name="p1556131520505"></a><a name="p1556131520505"></a>解码</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p12561915105011"><a name="p12561915105011"></a><a name="p12561915105011"></a>7.5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1156415205014"><a name="p1156415205014"></a><a name="p1156415205014"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p156315175013"><a name="p156315175013"></a><a name="p156315175013"></a>4Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p205631525011"><a name="p205631525011"></a><a name="p205631525011"></a>13k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p1056315115015"><a name="p1056315115015"></a><a name="p1056315115015"></a>1.14k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.7 "><p id="p75621575018"><a name="p75621575018"></a><a name="p75621575018"></a>64kbps</p>
</td>
</tr>
<tr id="row1455613520716"><td class="cellrowborder" rowspan="6" valign="top" width="10.58105810581058%" headers="mcps1.******* "><p id="p15556335276"><a name="p15556335276"></a><a name="p15556335276"></a>l2hc</p>
</td>
<td class="cellrowborder" rowspan="3" valign="top" width="8.870887088708871%" headers="mcps1.******* "><p id="p855617355710"><a name="p855617355710"></a><a name="p855617355710"></a>编码</p>
</td>
<td class="cellrowborder" valign="top" width="10.47104710471047%" headers="mcps1.******* "><p id="p1355615351779"><a name="p1355615351779"></a><a name="p1355615351779"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" width="10.901090109010902%" headers="mcps1.1.9.1.4 "><p id="p65561035870"><a name="p65561035870"></a><a name="p65561035870"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.5 "><p id="p175562352718"><a name="p175562352718"></a><a name="p175562352718"></a>35Mcps</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.6 "><p id="p205562352719"><a name="p205562352719"></a><a name="p205562352719"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="17.18171817181718%" headers="mcps1.1.9.1.7 "><p id="p125561435778"><a name="p125561435778"></a><a name="p125561435778"></a>7k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="16.99169916991699%" headers="mcps1.1.9.1.8 "><p id="p1755615351573"><a name="p1755615351573"></a><a name="p1755615351573"></a>64kbps</p>
</td>
</tr>
<tr id="row8141842572"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p914134219719"><a name="p914134219719"></a><a name="p914134219719"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p16141114218719"><a name="p16141114218719"></a><a name="p16141114218719"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p11411242478"><a name="p11411242478"></a><a name="p11411242478"></a>37.5Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p121411342376"><a name="p121411342376"></a><a name="p121411342376"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p1114117422716"><a name="p1114117422716"></a><a name="p1114117422716"></a>7k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p161411342573"><a name="p161411342573"></a><a name="p161411342573"></a>64kbps</p>
</td>
</tr>
<tr id="row2144202115914"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1815012207917"><a name="p1815012207917"></a><a name="p1815012207917"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p7150102012913"><a name="p7150102012913"></a><a name="p7150102012913"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p101504201497"><a name="p101504201497"></a><a name="p101504201497"></a>47Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p115018201295"><a name="p115018201295"></a><a name="p115018201295"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p1815012201914"><a name="p1815012201914"></a><a name="p1815012201914"></a>7k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p61507201097"><a name="p61507201097"></a><a name="p61507201097"></a>96kbps</p>
</td>
</tr>
<tr id="row181421921697"><td class="cellrowborder" rowspan="3" valign="top" headers="mcps1.******* "><p id="p1123552872111"><a name="p1123552872111"></a><a name="p1123552872111"></a>解码</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p41507201793"><a name="p41507201793"></a><a name="p41507201793"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p815092011910"><a name="p815092011910"></a><a name="p815092011910"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p101501220494"><a name="p101501220494"></a><a name="p101501220494"></a>13.6Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p1815072010915"><a name="p1815072010915"></a><a name="p1815072010915"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p61506201691"><a name="p61506201691"></a><a name="p61506201691"></a>3.7k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.7 "><p id="p815032018911"><a name="p815032018911"></a><a name="p815032018911"></a>64kbps</p>
</td>
</tr>
<tr id="row09581244911"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p14878245919"><a name="p14878245919"></a><a name="p14878245919"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p178762410920"><a name="p178762410920"></a><a name="p178762410920"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p11878242912"><a name="p11878242912"></a><a name="p11878242912"></a>24Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p4874241895"><a name="p4874241895"></a><a name="p4874241895"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p1187524592"><a name="p1187524592"></a><a name="p1187524592"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p98718248917"><a name="p98718248917"></a><a name="p98718248917"></a>64kbps</p>
</td>
</tr>
<tr id="row99582242915"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p98722416914"><a name="p98722416914"></a><a name="p98722416914"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p148714249917"><a name="p148714249917"></a><a name="p148714249917"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p4873249917"><a name="p4873249917"></a><a name="p4873249917"></a>29Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p14873241691"><a name="p14873241691"></a><a name="p14873241691"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p0877240913"><a name="p0877240913"></a><a name="p0877240913"></a>10k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p58810242096"><a name="p58810242096"></a><a name="p58810242096"></a>96kbps</p>
</td>
</tr>
<tr id="row7813452172614"><td class="cellrowborder" rowspan="10" valign="top" width="10.58105810581058%" headers="mcps1.******* "><p id="p396915302299"><a name="p396915302299"></a><a name="p396915302299"></a>opus</p>
</td>
<td class="cellrowborder" rowspan="5" valign="top" width="8.870887088708871%" headers="mcps1.******* "><p id="p152701943102912"><a name="p152701943102912"></a><a name="p152701943102912"></a>编码</p>
</td>
<td class="cellrowborder" valign="top" width="10.47104710471047%" headers="mcps1.******* "><p id="p12813155215264"><a name="p12813155215264"></a><a name="p12813155215264"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" width="10.901090109010902%" headers="mcps1.1.9.1.4 "><p id="p9813145212615"><a name="p9813145212615"></a><a name="p9813145212615"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.5 "><p id="p9813452162613"><a name="p9813452162613"></a><a name="p9813452162613"></a>36Mcps</p>
</td>
<td class="cellrowborder" valign="top" width="12.5012501250125%" headers="mcps1.1.9.1.6 "><p id="p15813115212619"><a name="p15813115212619"></a><a name="p15813115212619"></a>28k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="17.18171817181718%" headers="mcps1.1.9.1.7 "><p id="p5813135272610"><a name="p5813135272610"></a><a name="p5813135272610"></a>7.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" width="16.99169916991699%" headers="mcps1.1.9.1.8 "><p id="p2813145292620"><a name="p2813145292620"></a><a name="p2813145292620"></a>64kbps</p>
</td>
</tr>
<tr id="row72547310278"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p52756232711"><a name="p52756232711"></a><a name="p52756232711"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p112751824271"><a name="p112751824271"></a><a name="p112751824271"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p192751126275"><a name="p192751126275"></a><a name="p192751126275"></a>52Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p4275421278"><a name="p4275421278"></a><a name="p4275421278"></a>28k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p12275922273"><a name="p12275922273"></a><a name="p12275922273"></a>7.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p1027512192711"><a name="p1027512192711"></a><a name="p1027512192711"></a>64kbps</p>
</td>
</tr>
<tr id="row6402197172719"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p963566112710"><a name="p963566112710"></a><a name="p963566112710"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p5635106152715"><a name="p5635106152715"></a><a name="p5635106152715"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p363517613270"><a name="p363517613270"></a><a name="p363517613270"></a>41Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p16635669273"><a name="p16635669273"></a><a name="p16635669273"></a>28k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p363556162718"><a name="p363556162718"></a><a name="p363556162718"></a>7.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p1663510616275"><a name="p1663510616275"></a><a name="p1663510616275"></a>64kbps</p>
</td>
</tr>
<tr id="row1840277182711"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p10636764275"><a name="p10636764275"></a><a name="p10636764275"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p66369611274"><a name="p66369611274"></a><a name="p66369611274"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p46365612714"><a name="p46365612714"></a><a name="p46365612714"></a>55Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p116367614271"><a name="p116367614271"></a><a name="p116367614271"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p563618652715"><a name="p563618652715"></a><a name="p563618652715"></a>7.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p19636461272"><a name="p19636461272"></a><a name="p19636461272"></a>64kbps</p>
</td>
</tr>
<tr id="row124341997275"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p206685817276"><a name="p206685817276"></a><a name="p206685817276"></a>2.5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p4668281274"><a name="p4668281274"></a><a name="p4668281274"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p176689842712"><a name="p176689842712"></a><a name="p176689842712"></a>45Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p156681386279"><a name="p156681386279"></a><a name="p156681386279"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p1466919892710"><a name="p1466919892710"></a><a name="p1466919892710"></a>7.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p116698814278"><a name="p116698814278"></a><a name="p116698814278"></a>64kbps</p>
</td>
</tr>
<tr id="row5434129122712"><td class="cellrowborder" rowspan="5" valign="top" headers="mcps1.******* "><p id="p1056712485296"><a name="p1056712485296"></a><a name="p1056712485296"></a>解码</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p18669118192712"><a name="p18669118192712"></a><a name="p18669118192712"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p156697822720"><a name="p156697822720"></a><a name="p156697822720"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p3669118162711"><a name="p3669118162711"></a><a name="p3669118162711"></a>21.5Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p46690813273"><a name="p46690813273"></a><a name="p46690813273"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p116691388276"><a name="p116691388276"></a><a name="p116691388276"></a>9.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.7 "><p id="p5669887272"><a name="p5669887272"></a><a name="p5669887272"></a>64kbps</p>
</td>
</tr>
<tr id="row1655331122712"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1979611022716"><a name="p1979611022716"></a><a name="p1979611022716"></a>10ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p16796191018274"><a name="p16796191018274"></a><a name="p16796191018274"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p13796191092713"><a name="p13796191092713"></a><a name="p13796191092713"></a>25Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p079615103272"><a name="p079615103272"></a><a name="p079615103272"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p167964103276"><a name="p167964103276"></a><a name="p167964103276"></a>9.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p9796181017278"><a name="p9796181017278"></a><a name="p9796181017278"></a>64kbps</p>
</td>
</tr>
<tr id="row19553911142711"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p4796201052710"><a name="p4796201052710"></a><a name="p4796201052710"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p1879621072712"><a name="p1879621072712"></a><a name="p1879621072712"></a>16kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p107961510112712"><a name="p107961510112712"></a><a name="p107961510112712"></a>25.3Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p207961610192715"><a name="p207961610192715"></a><a name="p207961610192715"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p19796131013271"><a name="p19796131013271"></a><a name="p19796131013271"></a>9.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p16796141052716"><a name="p16796141052716"></a><a name="p16796141052716"></a>64kbps</p>
</td>
</tr>
<tr id="row2365219172914"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p15558141818298"><a name="p15558141818298"></a><a name="p15558141818298"></a>5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p55582018192910"><a name="p55582018192910"></a><a name="p55582018192910"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p135581618182913"><a name="p135581618182913"></a><a name="p135581618182913"></a>30Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p1555811183295"><a name="p1555811183295"></a><a name="p1555811183295"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p95581018152910"><a name="p95581018152910"></a><a name="p95581018152910"></a>9.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p19558131810298"><a name="p19558131810298"></a><a name="p19558131810298"></a>64kbps</p>
</td>
</tr>
<tr id="row736411922915"><td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p175581318142912"><a name="p175581318142912"></a><a name="p175581318142912"></a>2.5ms</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p555820187294"><a name="p555820187294"></a><a name="p555820187294"></a>48kHz</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.******* "><p id="p11558918192914"><a name="p11558918192914"></a><a name="p11558918192914"></a>30.2Mcps</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.4 "><p id="p855919184298"><a name="p855919184298"></a><a name="p855919184298"></a>30k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.5 "><p id="p17559161832914"><a name="p17559161832914"></a><a name="p17559161832914"></a>9.1k Byte</p>
</td>
<td class="cellrowborder" valign="top" headers="mcps1.1.9.1.6 "><p id="p18559161812918"><a name="p18559161812918"></a><a name="p18559161812918"></a>64kbps</p>
</td>
</tr>
</tbody>
</table>

